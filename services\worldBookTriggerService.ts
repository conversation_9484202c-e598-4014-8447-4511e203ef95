import { CustomNarrativePrimaryElement, CustomNarrativeSubElement } from '../types';

export interface WorldBookActivationResult {
  activatedElements: CustomNarrativeSubElement[];
  totalTokensUsed: number;
  activationLog: string[];
  recursiveActivations: CustomNarrativeSubElement[];
}

export interface WorldBookScanOptions {
  scanDepth?: number;
  tokenBudget?: number;
  recursiveScanning?: boolean;
  caseSensitive?: boolean;
  matchWholeWords?: boolean;
  includeNames?: boolean;
  minActivations?: number;
  maxDepth?: number;
  maxRecursionSteps?: number;
}

export class WorldBookTriggerService {
  private activationHistory: Map<string, number> = new Map();
  private stickyEntries: Map<string, { entry: CustomNarrativeSubElement; remainingMessages: number }> = new Map();
  private cooldownEntries: Map<string, number> = new Map();

  /**
   * Scans text for world book triggers and returns activated entries
   */
  scanForTriggers(
    text: string,
    worldBookElements: CustomNarrativePrimaryElement[],
    options: WorldBookScanOptions = {},
    messageCount: number = 0
  ): WorldBookActivationResult {
    const {
      scanDepth = 4,
      tokenBudget = 2048,
      recursiveScanning = false,
      caseSensitive = false,
      matchWholeWords = true,
      includeNames = true,
      minActivations = 0,
      maxDepth = 100,
      maxRecursionSteps = 0
    } = options;

    const activatedElements: CustomNarrativeSubElement[] = [];
    const activationLog: string[] = [];
    const recursiveActivations: CustomNarrativeSubElement[] = [];
    let totalTokensUsed = 0;

    // Process sticky entries first
    this.processStickyEntries(activatedElements, activationLog);

    // Prepare scan text
    const scanText = this.prepareScanText(text, scanDepth, includeNames);
    
    // Get all active world book entries
    const allEntries = this.getAllActiveEntries(worldBookElements);
    
    // Filter entries by delay
    const eligibleEntries = allEntries.filter(entry => 
      this.isEntryEligible(entry, messageCount)
    );

    // First pass: direct keyword matching
    const directMatches = this.findDirectMatches(
      scanText, 
      eligibleEntries, 
      caseSensitive, 
      matchWholeWords
    );

    // Apply probability filtering
    const probabilityFiltered = this.applyProbabilityFilter(directMatches);

    // Apply inclusion groups
    const groupFiltered = this.applyInclusionGroups(probabilityFiltered);

    // Sort by priority and add to activated elements
    const sortedMatches = this.sortByPriority(groupFiltered);
    
    for (const entry of sortedMatches) {
      if (totalTokensUsed + this.estimateTokens(entry.value) <= tokenBudget) {
        activatedElements.push(entry);
        totalTokensUsed += this.estimateTokens(entry.value);
        activationLog.push(`Activated: ${entry.key} (direct match)`);
        
        // Apply timed effects
        this.applyTimedEffects(entry, messageCount);
      } else {
        activationLog.push(`Skipped: ${entry.key} (token budget exceeded)`);
        break;
      }
    }

    // Recursive scanning if enabled
    if (recursiveScanning && maxRecursionSteps > 0) {
      const recursiveResult = this.performRecursiveScanning(
        activatedElements,
        allEntries,
        options,
        messageCount,
        tokenBudget - totalTokensUsed,
        maxRecursionSteps
      );
      
      recursiveActivations.push(...recursiveResult.activations);
      activationLog.push(...recursiveResult.log);
      totalTokensUsed += recursiveResult.tokensUsed;
    }

    // Handle minimum activations
    if (minActivations > 0 && activatedElements.length < minActivations) {
      const additionalResult = this.findAdditionalActivations(
        text,
        allEntries,
        activatedElements,
        minActivations - activatedElements.length,
        tokenBudget - totalTokensUsed,
        maxDepth,
        options
      );
      
      activatedElements.push(...additionalResult.activations);
      activationLog.push(...additionalResult.log);
      totalTokensUsed += additionalResult.tokensUsed;
    }

    return {
      activatedElements,
      totalTokensUsed,
      activationLog,
      recursiveActivations
    };
  }

  private prepareScanText(text: string, scanDepth: number, includeNames: boolean): string {
    // Split text into messages and take the last scanDepth messages
    const messages = text.split('\n').filter(line => line.trim());
    const recentMessages = messages.slice(-scanDepth);
    
    if (includeNames) {
      return recentMessages.join('\n');
    } else {
      // Remove speaker names (assuming format "Name: message")
      return recentMessages
        .map(msg => msg.replace(/^[^:]+:\s*/, ''))
        .join('\n');
    }
  }

  private getAllActiveEntries(worldBookElements: CustomNarrativePrimaryElement[]): CustomNarrativeSubElement[] {
    const entries: CustomNarrativeSubElement[] = [];
    
    for (const element of worldBookElements) {
      if (element.isActive) {
        for (const subElement of element.subElements) {
          if (subElement.isActive) {
            entries.push(subElement);
          }
        }
      }
    }
    
    return entries;
  }

  private isEntryEligible(entry: CustomNarrativeSubElement, messageCount: number): boolean {
    const sillyData = entry.sillyTavernData;
    if (!sillyData) return true;

    // Check delay
    if (sillyData.delay && messageCount < sillyData.delay) {
      return false;
    }

    // Check cooldown
    const cooldownKey = entry.id;
    const cooldownEnd = this.cooldownEntries.get(cooldownKey);
    if (cooldownEnd && messageCount < cooldownEnd) {
      return false;
    }

    return true;
  }

  private findDirectMatches(
    text: string,
    entries: CustomNarrativeSubElement[],
    caseSensitive: boolean,
    matchWholeWords: boolean
  ): CustomNarrativeSubElement[] {
    const matches: CustomNarrativeSubElement[] = [];
    const searchText = caseSensitive ? text : text.toLowerCase();

    for (const entry of entries) {
      const sillyData = entry.sillyTavernData;
      const keys = sillyData?.keys || [entry.key];
      
      let matched = false;
      
      for (const key of keys) {
        if (this.testKeyMatch(searchText, key, caseSensitive, matchWholeWords)) {
          // Check secondary keys if present
          if (sillyData?.keysecondary?.length) {
            if (this.testSecondaryKeys(searchText, sillyData.keysecondary, sillyData.selectiveLogic || 0, caseSensitive, matchWholeWords)) {
              matched = true;
              break;
            }
          } else {
            matched = true;
            break;
          }
        }
      }
      
      if (matched) {
        matches.push(entry);
      }
    }

    return matches;
  }

  private testKeyMatch(text: string, key: string, caseSensitive: boolean, matchWholeWords: boolean): boolean {
    const searchKey = caseSensitive ? key : key.toLowerCase();
    
    // Check if key is a regex pattern
    if (this.isRegexPattern(key)) {
      try {
        const flags = caseSensitive ? 'g' : 'gi';
        const regex = new RegExp(key.slice(1, -1), flags); // Remove / delimiters
        return regex.test(text);
      } catch (e) {
        console.warn('Invalid regex pattern in world book key:', key);
        return false;
      }
    }
    
    if (matchWholeWords) {
      const wordBoundaryRegex = new RegExp(`\\b${this.escapeRegex(searchKey)}\\b`, caseSensitive ? 'g' : 'gi');
      return wordBoundaryRegex.test(text);
    } else {
      return text.includes(searchKey);
    }
  }

  private testSecondaryKeys(
    text: string,
    secondaryKeys: string[],
    selectiveLogic: number,
    caseSensitive: boolean,
    matchWholeWords: boolean
  ): boolean {
    const matches = secondaryKeys.map(key => 
      this.testKeyMatch(text, key, caseSensitive, matchWholeWords)
    );

    switch (selectiveLogic) {
      case 0: // AND ANY
        return matches.some(match => match);
      case 1: // AND ALL
        return matches.every(match => match);
      case 2: // NOT ANY
        return !matches.some(match => match);
      case 3: // NOT ALL
        return !matches.every(match => match);
      default:
        return matches.some(match => match);
    }
  }

  private isRegexPattern(key: string): boolean {
    return key.startsWith('/') && key.includes('/') && key.length > 2;
  }

  private escapeRegex(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  private applyProbabilityFilter(entries: CustomNarrativeSubElement[]): CustomNarrativeSubElement[] {
    return entries.filter(entry => {
      const sillyData = entry.sillyTavernData;
      if (!sillyData?.useProbability || !sillyData.probability) return true;
      
      const probability = sillyData.probability / 100;
      return Math.random() < probability;
    });
  }

  private applyInclusionGroups(entries: CustomNarrativeSubElement[]): CustomNarrativeSubElement[] {
    const groupMap = new Map<string, CustomNarrativeSubElement[]>();
    const ungroupedEntries: CustomNarrativeSubElement[] = [];

    // Group entries by inclusion group
    for (const entry of entries) {
      const group = entry.sillyTavernData?.group;
      if (group) {
        if (!groupMap.has(group)) {
          groupMap.set(group, []);
        }
        groupMap.get(group)!.push(entry);
      } else {
        ungroupedEntries.push(entry);
      }
    }

    const result = [...ungroupedEntries];

    // Select one entry from each group
    for (const [group, groupEntries] of groupMap) {
      if (groupEntries.length === 1) {
        result.push(groupEntries[0]);
      } else {
        // Use group scoring or random selection
        const selected = this.selectFromGroup(groupEntries);
        if (selected) {
          result.push(selected);
        }
      }
    }

    return result;
  }

  private selectFromGroup(entries: CustomNarrativeSubElement[]): CustomNarrativeSubElement | null {
    if (entries.length === 0) return null;
    if (entries.length === 1) return entries[0];

    // Check if any entry has group scoring enabled
    const useGroupScoring = entries.some(entry => entry.sillyTavernData?.use_group_scoring);
    
    if (useGroupScoring) {
      // Find entries with highest key match count (simplified implementation)
      const maxScore = Math.max(...entries.map(entry => 
        (entry.sillyTavernData?.keys?.length || 1)
      ));
      const topScorers = entries.filter(entry => 
        (entry.sillyTavernData?.keys?.length || 1) === maxScore
      );
      entries = topScorers;
    }

    // Weighted random selection based on group_weight
    const totalWeight = entries.reduce((sum, entry) => 
      sum + (entry.sillyTavernData?.group_weight || 100), 0
    );
    
    let random = Math.random() * totalWeight;
    
    for (const entry of entries) {
      random -= (entry.sillyTavernData?.group_weight || 100);
      if (random <= 0) {
        return entry;
      }
    }

    return entries[0]; // Fallback
  }

  private sortByPriority(entries: CustomNarrativeSubElement[]): CustomNarrativeSubElement[] {
    return entries.sort((a, b) => {
      const priorityA = a.sillyTavernData?.priority || 0;
      const priorityB = b.sillyTavernData?.priority || 0;
      
      if (priorityA !== priorityB) {
        return priorityB - priorityA; // Higher priority first
      }
      
      // Secondary sort by insertion order
      const orderA = a.sillyTavernData?.insertion_order || 0;
      const orderB = b.sillyTavernData?.insertion_order || 0;
      return orderA - orderB;
    });
  }

  private estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
  }

  private applyTimedEffects(entry: CustomNarrativeSubElement, messageCount: number): void {
    const sillyData = entry.sillyTavernData;
    if (!sillyData) return;

    // Apply sticky effect
    if (sillyData.sticky && sillyData.sticky > 0) {
      this.stickyEntries.set(entry.id, {
        entry,
        remainingMessages: sillyData.sticky
      });
    }

    // Apply cooldown effect
    if (sillyData.cooldown && sillyData.cooldown > 0) {
      const cooldownEnd = messageCount + (sillyData.sticky || 0) + sillyData.cooldown;
      this.cooldownEntries.set(entry.id, cooldownEnd);
    }
  }

  private processStickyEntries(activatedElements: CustomNarrativeSubElement[], activationLog: string[]): void {
    const toRemove: string[] = [];

    for (const [entryId, stickyData] of this.stickyEntries) {
      if (stickyData.remainingMessages > 0) {
        activatedElements.push(stickyData.entry);
        activationLog.push(`Activated: ${stickyData.entry.key} (sticky effect)`);
        
        stickyData.remainingMessages--;
        if (stickyData.remainingMessages <= 0) {
          toRemove.push(entryId);
        }
      } else {
        toRemove.push(entryId);
      }
    }

    // Clean up expired sticky entries
    toRemove.forEach(id => this.stickyEntries.delete(id));
  }

  private performRecursiveScanning(
    activatedElements: CustomNarrativeSubElement[],
    allEntries: CustomNarrativeSubElement[],
    options: WorldBookScanOptions,
    messageCount: number,
    remainingTokenBudget: number,
    maxRecursionSteps: number
  ): { activations: CustomNarrativeSubElement[]; log: string[]; tokensUsed: number } {
    const recursiveActivations: CustomNarrativeSubElement[] = [];
    const log: string[] = [];
    let tokensUsed = 0;

    // Get content from activated elements for recursive scanning
    const activatedContent = activatedElements.map(entry => entry.value).join('\n');
    
    if (activatedContent.trim() && maxRecursionSteps > 0) {
      const recursiveResult = this.scanForTriggers(
        activatedContent,
        [{ 
          id: 'recursive_scan', 
          name: 'Recursive Scan', 
          isActive: true, 
          subElements: allEntries.filter(entry => 
            !activatedElements.includes(entry) && 
            !entry.sillyTavernData?.prevent_recursion
          )
        }],
        { ...options, maxRecursionSteps: maxRecursionSteps - 1 },
        messageCount
      );

      for (const entry of recursiveResult.activatedElements) {
        if (tokensUsed + this.estimateTokens(entry.value) <= remainingTokenBudget) {
          recursiveActivations.push(entry);
          tokensUsed += this.estimateTokens(entry.value);
          log.push(`Activated: ${entry.key} (recursive)`);
        } else {
          log.push(`Skipped: ${entry.key} (recursive, token budget exceeded)`);
          break;
        }
      }
    }

    return { activations: recursiveActivations, log, tokensUsed };
  }

  private findAdditionalActivations(
    text: string,
    allEntries: CustomNarrativeSubElement[],
    alreadyActivated: CustomNarrativeSubElement[],
    needed: number,
    remainingTokenBudget: number,
    maxDepth: number,
    options: WorldBookScanOptions
  ): { activations: CustomNarrativeSubElement[]; log: string[]; tokensUsed: number } {
    const activations: CustomNarrativeSubElement[] = [];
    const log: string[] = [];
    let tokensUsed = 0;

    // Expand search to more messages if needed
    const messages = text.split('\n').filter(line => line.trim());
    const expandedDepth = Math.min(messages.length, maxDepth);
    
    for (let depth = (options.scanDepth || 4) + 1; depth <= expandedDepth && activations.length < needed; depth++) {
      const expandedText = this.prepareScanText(text, depth, options.includeNames || true);
      const availableEntries = allEntries.filter(entry => 
        !alreadyActivated.includes(entry) && !activations.includes(entry)
      );

      const matches = this.findDirectMatches(
        expandedText,
        availableEntries,
        options.caseSensitive || false,
        options.matchWholeWords !== false
      );

      for (const entry of matches) {
        if (activations.length >= needed) break;
        if (tokensUsed + this.estimateTokens(entry.value) <= remainingTokenBudget) {
          activations.push(entry);
          tokensUsed += this.estimateTokens(entry.value);
          log.push(`Activated: ${entry.key} (min activations, depth ${depth})`);
        }
      }
    }

    return { activations, log, tokensUsed };
  }

  /**
   * Clean up expired timed effects
   */
  cleanupTimedEffects(currentMessageCount: number): void {
    // Clean up expired cooldowns
    const expiredCooldowns: string[] = [];
    for (const [entryId, cooldownEnd] of this.cooldownEntries) {
      if (currentMessageCount >= cooldownEnd) {
        expiredCooldowns.push(entryId);
      }
    }
    expiredCooldowns.forEach(id => this.cooldownEntries.delete(id));
  }

  /**
   * Get current activation status for debugging
   */
  getActivationStatus(): {
    stickyEntries: Array<{ id: string; key: string; remainingMessages: number }>;
    cooldownEntries: Array<{ id: string; cooldownEnd: number }>;
  } {
    return {
      stickyEntries: Array.from(this.stickyEntries.entries()).map(([id, data]) => ({
        id,
        key: data.entry.key,
        remainingMessages: data.remainingMessages
      })),
      cooldownEntries: Array.from(this.cooldownEntries.entries()).map(([id, cooldownEnd]) => ({
        id,
        cooldownEnd
      }))
    };
  }
}

export const worldBookTriggerService = new WorldBookTriggerService();
