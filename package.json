{"name": "memoryable", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "local-server": "node local-server/index.cjs", "dev:local": "concurrently \"npm run local-server\" \"npm run dev\"", "setup": "node setup.cjs", "clean": "rm -rf node_modules package-lock.json && npm cache clean --force", "reinstall": "npm run clean && npm install", "type-check": "tsc --noEmit", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "fix-ts-errors": "node scripts/fix-typescript-errors.js", "analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"@google/genai": "^1.1.0", "axios": "^1.6.0", "cors": "^2.8.5", "dompurify": "^3.0.0", "express": "^4.18.0", "file-saver": "^2.0.5", "jszip": "^3.10.0", "marked": "^12.0.0", "multer": "^1.4.4", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-select": "^5.8.0", "uuid": "^9.0.0"}, "devDependencies": {"@types/cors": "^2.8.0", "@types/express": "^4.17.0", "@types/file-saver": "^2.0.0", "@types/multer": "^1.4.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-beautiful-dnd": "^13.1.0", "@types/react-dom": "^18.2.0", "@types/uuid": "^9.0.0", "@vitejs/plugin-react": "^4.0.0", "concurrently": "^8.2.0", "terser": "^5.41.0", "typescript": "^5.0.0", "vite": "^5.0.0"}}