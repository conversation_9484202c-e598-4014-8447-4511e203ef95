import {
  SillyTavernCharCard,
  SillyTavernCharCardEntry,
  SillyTavernEntry,
  SillyTavernCharBook,
  CharacterCardData,
  CustomNarrativePrimaryElement,
  RegexRule,
  RegexRuleScope
} from '../types';
import { extractCharaDataFromPng } from '../utils/pngCardParser';
import { localApiService } from './localApiService';

export interface EnhancedCharacterCard extends SillyTavernCharCard {
  // Enhanced fields for better compatibility
  version?: string;
  creator?: string;
  character_version?: string;
  extensions?: Record<string, any>;
  system_prompt?: string;
  post_history_instructions?: string;
  alternate_greetings?: string[];
  character_book?: EnhancedCharBook;
  lorebook?: EnhancedCharBook;
}

export interface EnhancedCharBook extends SillyTavernCharBook {
  // Enhanced world book features
  extensions?: Record<string, any>;
  global_priority?: number;
  character_filter?: string[];
  world_info_before?: string;
  world_info_after?: string;
  entries: EnhancedCharBookEntry[];
}

export interface EnhancedCharBookEntry extends SillyTavernCharCardEntry {
  // Enhanced entry features
  id?: string;
  uid?: number;
  priority?: number;
  order?: number;
  probability?: number;
  useProbability?: boolean;
  depth?: number;
  selectiveLogic?: number;
  comment_display?: string;
  filters?: string[];
  extensions?: Record<string, any>;
  case_sensitive?: boolean;
  match_whole_words?: boolean;
  use_group_scoring?: boolean;
  automation_id?: string;
  role?: number;
  vectorized?: boolean;
  prevent_recursion?: boolean;
}

export interface CharacterRelationship {
  id: string;
  sourceCharacterId: string;
  targetCharacterId: string;
  relationshipType: 'friend' | 'enemy' | 'family' | 'romantic' | 'neutral' | 'unknown';
  strength: number; // -100 to 100
  description: string;
  history: Array<{
    timestamp: number;
    event: string;
    impact: number;
  }>;
}

export interface WorldBookHierarchy {
  id: string;
  parentId?: string;
  children: string[];
  depth: number;
  dependencies: string[];
  conflicts: string[];
}

export class SillyTavernService {
  private characterRelationships: Map<string, CharacterRelationship[]> = new Map();
  private worldBookHierarchy: Map<string, WorldBookHierarchy> = new Map();

  // Enhanced character card processing with full SillyTavern v2 compatibility
  async processCharacterCard(
    cardData: EnhancedCharacterCard,
    fileName: string
  ): Promise<{
    characterData: CharacterCardData;
    worldBookElements: CustomNarrativePrimaryElement[];
    regexRules: RegexRule[];
    relationships: CharacterRelationship[];
    metadata: any;
  }> {
    // Process basic character data with v2 compatibility
    const characterData: CharacterCardData = {
      characterName: this.extractCharacterName(cardData),
      characterDescription: this.selectBestDescription(cardData),
      characterPersonality: this.extractPersonality(cardData),
      characterOpeningMessage: this.selectBestGreeting(cardData),
      characterScenario: this.extractScenario(cardData),
      characterExampleDialogue: this.extractExampleDialogue(cardData),
      characterPortraitKeywords: this.generatePortraitKeywords(cardData)
    };

    // Process world book data with hierarchical structure
    const worldBookElements = await this.processWorldBookDataEnhanced(cardData, fileName);

    // Extract regex rules from extensions or embedded data
    const regexRules = this.extractRegexRules(cardData, fileName);

    // Extract relationships
    const relationships = this.extractCharacterRelationships(cardData);

    // Extract comprehensive metadata
    const metadata = this.extractComprehensiveMetadata(cardData, fileName);

    return {
      characterData,
      worldBookElements,
      regexRules,
      relationships,
      metadata
    };
  }

  // Enhanced extraction methods for SillyTavern v2 compatibility
  private extractCharacterName(cardData: EnhancedCharacterCard): string {
    return cardData.data?.name ||
           cardData.name ||
           cardData.char_name ||
           'Unknown Character';
  }

  private extractPersonality(cardData: EnhancedCharacterCard): string {
    return cardData.data?.personality ||
           cardData.personality ||
           cardData.char_persona ||
           '';
  }

  private extractScenario(cardData: EnhancedCharacterCard): string {
    return cardData.data?.scenario ||
           cardData.scenario ||
           cardData.world_scenario ||
           '';
  }

  private extractExampleDialogue(cardData: EnhancedCharacterCard): string {
    return cardData.data?.mes_example ||
           cardData.mes_example ||
           cardData.example_dialogue ||
           '';
  }

  private extractComprehensiveMetadata(cardData: EnhancedCharacterCard, fileName: string): any {
    const baseFileName = fileName.replace(/\.(json|jsonc|png)$/i, '');

    return {
      // Version information
      spec: cardData.spec || 'chara_card_v2',
      spec_version: cardData.spec_version || '2.0',
      version: cardData.character_version || cardData.data?.character_version || '1.0',

      // Creator information
      creator: cardData.creator || cardData.data?.creator || 'Unknown',
      creator_notes: cardData.creator_notes || cardData.data?.creator_notes || '',

      // Character metadata
      character_note: cardData.character_note || cardData.data?.character_note || '',
      create_date: cardData.create_date || new Date().toISOString(),

      // Greetings and variations
      alternateGreetings: cardData.alternate_greetings || cardData.data?.alternate_greetings || [],

      // System prompts
      systemPrompt: cardData.system_prompt || cardData.data?.system_prompt || '',
      postHistoryInstructions: cardData.post_history_instructions || cardData.data?.post_history_instructions || '',

      // Tags and categorization
      tags: cardData.tags || cardData.data?.tags || [],

      // Extensions and custom data
      extensions: {
        ...cardData.extensions,
        ...cardData.data?.extensions,
        memoryable_import: {
          sourceFileName: fileName,
          baseFileName,
          importDate: new Date().toISOString(),
          importVersion: '1.0'
        }
      },

      // Additional metadata
      avatar: cardData.avatar || '',
      chat: cardData.chat || '',
      talkativeness: cardData.talkativeness || '',
      fav: cardData.fav || false
    };
  }

  private selectBestDescription(cardData: EnhancedCharacterCard): string {
    // Priority: description > char_persona > fallback
    const candidates = [
      cardData.description,
      cardData.char_persona
    ].filter(Boolean);

    if (candidates.length === 0) return '';

    // Return the longest non-empty description
    return candidates.reduce((best, current) => 
      current.length > best.length ? current : best
    );
  }

  private selectBestGreeting(cardData: EnhancedCharacterCard): string {
    const greetings = [
      cardData.first_mes,
      cardData.char_greeting,
      ...(cardData.alternate_greetings || [])
    ].filter(Boolean);

    return greetings[0] || '';
  }

  private generatePortraitKeywords(cardData: EnhancedCharacterCard): string {
    const keywords: string[] = [];

    // Add character name
    if (cardData.name) keywords.push(cardData.name);

    // Add tags
    if (cardData.tags) keywords.push(...cardData.tags);

    // Extract keywords from description
    const description = this.selectBestDescription(cardData);
    const extractedKeywords = this.extractKeywordsFromText(description);
    keywords.push(...extractedKeywords);

    return keywords.join(', ');
  }

  private extractKeywordsFromText(text: string): string[] {
    if (!text) return [];

    // Simple keyword extraction - can be enhanced with NLP
    const commonWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should']);
    
    const words = text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2 && !commonWords.has(word))
      .slice(0, 10); // Limit to 10 keywords

    return words;
  }

  // Enhanced world book processing with hierarchical structure
  private async processWorldBookDataEnhanced(
    cardData: EnhancedCharacterCard,
    fileName: string
  ): Promise<CustomNarrativePrimaryElement[]> {
    const elements: CustomNarrativePrimaryElement[] = [];
    const baseFileName = fileName.replace(/\.(json|jsonc|png)$/i, '');
    const characterName = this.extractCharacterName(cardData);

    // Create main hierarchy container
    const mainContainer: CustomNarrativePrimaryElement = {
      id: `wb_main_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
      name: baseFileName,
      isActive: true,
      subElements: [],
      metadata: {
        sourceType: 'character_book',
        sourceFileName: fileName,
        characterName,
        importDate: new Date().toISOString(),
        hierarchyLevel: 0,
        childIds: []
      }
    };

    // Process character_book with enhanced metadata
    if (cardData.character_book?.entries?.length || cardData.data?.character_book?.entries?.length) {
      const charBook = cardData.character_book || cardData.data?.character_book;
      if (charBook) {
        const element = this.createEnhancedWorldBookElement(
          charBook,
          'Character Book',
          baseFileName,
          characterName,
          fileName,
          'character_book'
        );
        elements.push(element);
        mainContainer.metadata!.childIds!.push(element.id);
      }
    }

    // Process lorebook with enhanced metadata
    if (cardData.lorebook?.entries?.length) {
      const element = this.createEnhancedWorldBookElement(
        cardData.lorebook,
        'Lorebook',
        baseFileName,
        characterName,
        fileName,
        'lorebook'
      );
      elements.push(element);
      mainContainer.metadata!.childIds!.push(element.id);
    }

    // Process direct entries
    if (cardData.entries) {
      const entriesArray = Array.isArray(cardData.entries)
        ? cardData.entries
        : Object.values(cardData.entries);

      if (entriesArray.length > 0) {
        const element = this.createWorldBookElementFromEntriesEnhanced(
          entriesArray,
          'World Info',
          baseFileName,
          characterName,
          fileName
        );
        elements.push(element);
        mainContainer.metadata!.childIds!.push(element.id);
      }
    }

    // Add main container only if it has children and there are actual elements
    if (mainContainer.metadata!.childIds!.length > 0 && elements.length > 0) {
      elements.unshift(mainContainer);
    }

    return elements;
  }

  // Legacy method for backward compatibility
  private async processWorldBookData(
    cardData: EnhancedCharacterCard,
    fileName: string
  ): Promise<CustomNarrativePrimaryElement[]> {
    return this.processWorldBookDataEnhanced(cardData, fileName);
  }

  // Enhanced world book element creation with full SillyTavern metadata
  private createEnhancedWorldBookElement(
    charBook: EnhancedCharBook,
    elementType: string,
    baseFileName: string,
    characterName: string,
    sourceFileName: string,
    sourceType: 'character_book' | 'lorebook'
  ): CustomNarrativePrimaryElement {
    const element: CustomNarrativePrimaryElement = {
      id: `wb_${sourceType}_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
      name: `${baseFileName} - ${elementType}`,
      isActive: true,
      subElements: [],
      metadata: {
        sourceType,
        sourceFileName,
        characterName,
        importDate: new Date().toISOString(),
        hierarchyLevel: 1,
        description: charBook.description || `${elementType} from ${characterName}`,
        // World book settings from SillyTavern
        scan_depth: charBook.scan_depth || 4,
        token_budget: charBook.token_budget || 2048,
        recursive_scanning: charBook.recursive_scanning || false,
        case_sensitive: charBook.case_sensitive || false,
        match_whole_words: charBook.match_whole_words || true,
        include_names: charBook.include_names !== false,
        min_activations: charBook.min_activations || 0,
        max_depth: charBook.max_depth || 100,
        max_recursion_steps: charBook.max_recursion_steps || 0,
        alert_on_overflow: charBook.alert_on_overflow || false,
        extensions: charBook.extensions || {},
        sillyTavernSettings: {
          name: charBook.name || elementType,
          global_priority: charBook.global_priority || 0,
          character_filter: charBook.character_filter || [],
          world_info_before: charBook.world_info_before || '',
          world_info_after: charBook.world_info_after || ''
        }
      }
    };

    charBook.entries.forEach((entry, index) => {
      const keys = entry.keys || [];
      const displayKey = entry.comment || entry.comment_display || (keys.length > 0 ? keys[0] : `Entry ${index + 1}`);

      element.subElements.push({
        id: `sub_${element.id}_${index}`,
        key: displayKey.trim(),
        value: (entry.content || '').trim(),
        isActive: !entry.disable,
        sillyTavernData: {
          keys: entry.keys || [],
          comment: entry.comment || '',
          priority: entry.priority || (100 - index),
          order: entry.order !== undefined ? entry.order : index,
          probability: entry.probability || 100,
          depth: entry.depth || 4,
          selectiveLogic: entry.selectiveLogic || 0,
          case_sensitive: entry.case_sensitive || false,
          match_whole_words: entry.match_whole_words !== false,
          insertion_order: entry.insertion_order || entry.order || index,
          insertion_position: entry.insertion_position || entry.position || 'after_char',
          sticky: entry.sticky || 0,
          cooldown: entry.cooldown || 0,
          delay: entry.delay || 0,
          group: entry.group || '',
          group_weight: entry.group_weight || 100,
          keysecondary: entry.keysecondary || [],
          filters: entry.filters || [],
          extensions: entry.extensions || {},
          automation_id: entry.automation_id || '',
          prevent_recursion: entry.prevent_recursion || false,
          vectorized: entry.vectorized || false,
          use_group_scoring: entry.use_group_scoring || false,
          useProbability: entry.useProbability || false,
          role: entry.role || 0,
          uid: entry.uid || index,
          scan_depth: entry.scan_depth,
          token_budget: entry.token_budget
        }
      });
    });

    return element;
  }

  // Legacy method for backward compatibility
  private createWorldBookElement(
    charBook: EnhancedCharBook,
    name: string
  ): CustomNarrativePrimaryElement {
    const baseFileName = name.replace(/^\[([^\]]+)\].*/, '$1');
    const characterName = baseFileName;
    return this.createEnhancedWorldBookElement(
      charBook,
      'World Book',
      baseFileName,
      characterName,
      name,
      'character_book'
    );
  }

  // Enhanced method for processing direct entries
  private createWorldBookElementFromEntriesEnhanced(
    entries: (SillyTavernCharCardEntry | SillyTavernEntry)[],
    elementType: string,
    baseFileName: string,
    characterName: string,
    sourceFileName: string
  ): CustomNarrativePrimaryElement {
    const element: CustomNarrativePrimaryElement = {
      id: `wb_entries_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
      name: `${baseFileName} - ${elementType}`,
      isActive: true,
      subElements: [],
      metadata: {
        sourceType: 'direct_entries',
        sourceFileName,
        characterName,
        importDate: new Date().toISOString(),
        hierarchyLevel: 1,
        description: `${elementType} entries from ${characterName}`,
        scan_depth: 4,
        token_budget: 2048,
        recursive_scanning: false,
        case_sensitive: false,
        match_whole_words: true,
        include_names: true
      }
    };

    entries.forEach((entry, index) => {
      const charCardEntry = entry as SillyTavernCharCardEntry;
      const sillyEntry = entry as SillyTavernEntry;

      const keys = charCardEntry.keys || sillyEntry.key || [];
      const displayKey = entry.comment || (keys.length > 0 ? keys[0] : `Entry ${index + 1}`);

      element.subElements.push({
        id: `sub_${element.id}_${index}`,
        key: displayKey.trim(),
        value: (entry.content || '').trim(),
        isActive: !entry.disable,
        sillyTavernData: {
          keys: Array.isArray(keys) ? keys : [keys].filter(Boolean),
          comment: entry.comment || '',
          priority: charCardEntry.priority || (100 - index),
          order: charCardEntry.order !== undefined ? charCardEntry.order : index,
          probability: charCardEntry.probability || 100,
          depth: charCardEntry.depth || 4,
          selectiveLogic: charCardEntry.selectiveLogic || 0,
          case_sensitive: charCardEntry.case_sensitive || false,
          match_whole_words: charCardEntry.match_whole_words !== false,
          insertion_order: charCardEntry.insertion_order || index,
          insertion_position: charCardEntry.insertion_position || charCardEntry.position || 'after_char',
          keysecondary: charCardEntry.keysecondary || sillyEntry.keysecondary || [],
          extensions: charCardEntry.extensions || {},
          uid: charCardEntry.uid || index
        }
      });
    });

    return element;
  }

  // Legacy method for backward compatibility
  private createWorldBookElementFromEntries(
    entries: (SillyTavernCharCardEntry | SillyTavernEntry)[],
    name: string
  ): CustomNarrativePrimaryElement {
    const baseFileName = name.replace(/^\[([^\]]+)\].*/, '$1');
    const characterName = baseFileName;
    return this.createWorldBookElementFromEntriesEnhanced(
      entries,
      'World Info',
      baseFileName,
      characterName,
      name
    );
  }

  // Extract regex rules from character card extensions or embedded data
  private extractRegexRules(cardData: EnhancedCharacterCard, fileName: string): RegexRule[] {
    const rules: RegexRule[] = [];
    const baseFileName = fileName.replace(/\.(json|jsonc|png)$/i, '');

    // Check extensions for regex rules
    const extensions = cardData.extensions || cardData.data?.extensions || {};

    // Look for common regex rule patterns in extensions
    if (extensions.regex_rules || extensions.regexRules) {
      const regexData = extensions.regex_rules || extensions.regexRules;
      if (Array.isArray(regexData)) {
        regexData.forEach((rule: any, index: number) => {
          if (rule.pattern && rule.replacement !== undefined) {
            rules.push({
              id: `regex_import_${Date.now()}_${index}_${Math.random().toString(36).substring(2, 7)}`,
              name: rule.name || `${baseFileName} Regex ${index + 1}`,
              pattern: rule.pattern,
              replacement: rule.replacement,
              flags: rule.flags || 'gi',
              scope: rule.scope || 'all',
              isActive: rule.isActive !== false,
              isDisplayOnly: rule.isDisplayOnly || false,
              trimInput: rule.trimInput || '',
              description: rule.description || `Imported from ${fileName}`
            });
          }
        });
      }
    }

    // Look for SillyTavern-style regex in world book entries
    const allBooks = [
      cardData.character_book,
      cardData.data?.character_book,
      cardData.lorebook
    ].filter(Boolean);

    allBooks.forEach(book => {
      if (book?.entries) {
        book.entries.forEach((entry, entryIndex) => {
          // Check if entry content contains regex patterns
          if (entry.content && this.containsRegexPattern(entry.content)) {
            const extractedRules = this.extractRegexFromContent(entry.content, baseFileName, entryIndex);
            rules.push(...extractedRules);
          }
        });
      }
    });

    return rules;
  }

  private containsRegexPattern(content: string): boolean {
    // Look for common regex indicators
    const regexIndicators = [
      /\/.*\/[gimuy]*\s*->\s*.*/,  // /pattern/flags -> replacement
      /regex\s*[:=]\s*\/.*\//i,    // regex: /pattern/
      /pattern\s*[:=]\s*\/.*\//i,  // pattern: /pattern/
      /replace\s*[:=]/i,           // replace: something
      /substitute\s*[:=]/i         // substitute: something
    ];

    return regexIndicators.some(pattern => pattern.test(content));
  }

  private extractRegexFromContent(content: string, baseFileName: string, entryIndex: number): RegexRule[] {
    const rules: RegexRule[] = [];

    // Extract regex patterns from content
    const regexMatches = content.match(/\/(.+?)\/([gimuy]*)\s*->\s*(.+?)(?:\n|$)/g);

    if (regexMatches) {
      regexMatches.forEach((match, matchIndex) => {
        const parts = match.match(/\/(.+?)\/([gimuy]*)\s*->\s*(.+?)(?:\n|$)/);
        if (parts) {
          rules.push({
            id: `regex_extracted_${Date.now()}_${entryIndex}_${matchIndex}_${Math.random().toString(36).substring(2, 7)}`,
            name: `${baseFileName} Extracted Regex ${entryIndex + 1}.${matchIndex + 1}`,
            pattern: parts[1],
            replacement: parts[3].trim(),
            flags: parts[2] || 'gi',
            scope: 'all',
            isActive: true,
            isDisplayOnly: false,
            trimInput: '',
            description: `Extracted from world book entry in ${baseFileName}`
          });
        }
      });
    }

    return rules;
  }

  private extractCharacterRelationships(cardData: EnhancedCharacterCard): CharacterRelationship[] {
    const relationships: CharacterRelationship[] = [];

    // Extract relationships from description and scenario
    const text = `${cardData.description || ''} ${cardData.scenario || ''} ${cardData.char_persona || ''}`;
    
    // Simple relationship extraction - can be enhanced with NLP
    const relationshipPatterns = [
      /(?:friend|ally|companion|partner)\s+(?:of|with|to)\s+([A-Z][a-z]+)/gi,
      /(?:enemy|rival|opponent)\s+(?:of|with|to)\s+([A-Z][a-z]+)/gi,
      /(?:sister|brother|mother|father|parent|child)\s+(?:of|to)\s+([A-Z][a-z]+)/gi,
      /(?:lover|boyfriend|girlfriend|spouse|husband|wife)\s+(?:of|to)\s+([A-Z][a-z]+)/gi
    ];

    relationshipPatterns.forEach((pattern, index) => {
      const matches = text.matchAll(pattern);
      for (const match of matches) {
        const targetName = match[1];
        const types = ['friend', 'enemy', 'family', 'romantic'] as const;
        
        relationships.push({
          id: `rel_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`,
          sourceCharacterId: cardData.name || 'unknown',
          targetCharacterId: targetName,
          relationshipType: types[index] || 'neutral',
          strength: 50, // Default neutral strength
          description: match[0],
          history: [{
            timestamp: Date.now(),
            event: 'Relationship extracted from character description',
            impact: 0
          }]
        });
      }
    });

    return relationships;
  }

  // Enhanced export functionality
  async exportCharacterCard(
    characterData: CharacterCardData,
    worldBookElements: CustomNarrativePrimaryElement[],
    relationships: CharacterRelationship[],
    format: 'json' | 'png' = 'json'
  ): Promise<Blob> {
    const enhancedCard: EnhancedCharacterCard = {
      name: characterData.characterName,
      description: characterData.characterDescription,
      personality: characterData.characterPersonality,
      first_mes: characterData.characterOpeningMessage,
      scenario: characterData.characterScenario,
      mes_example: characterData.characterExampleDialogue,
      tags: characterData.characterPortraitKeywords.split(',').map(tag => tag.trim()),
      version: '2.0',
      creator: 'MemoryAble',
      character_book: this.convertToCharBook(worldBookElements),
      extensions: {
        memoryable: {
          relationships,
          exportedAt: new Date().toISOString()
        }
      }
    };

    if (format === 'json') {
      const jsonString = JSON.stringify(enhancedCard, null, 2);
      return new Blob([jsonString], { type: 'application/json' });
    } else {
      // PNG export would require canvas manipulation
      // For now, return JSON blob
      const jsonString = JSON.stringify(enhancedCard, null, 2);
      return new Blob([jsonString], { type: 'application/json' });
    }
  }

  private convertToCharBook(elements: CustomNarrativePrimaryElement[]): EnhancedCharBook {
    const entries: EnhancedCharBookEntry[] = [];

    elements.forEach(element => {
      element.subElements.forEach((subElement, index) => {
        entries.push({
          keys: [subElement.key],
          comment: subElement.key,
          content: subElement.value,
          disable: !subElement.isActive,
          id: subElement.id,
          priority: 100 - index, // Higher priority for earlier entries
          order: index,
          depth: 4,
          selectiveLogic: 0,
          case_sensitive: false,
          match_whole_words: false
        });
      });
    });

    return {
      name: 'MemoryAble World Book',
      description: 'Exported from MemoryAble',
      scan_depth: 4,
      token_budget: 2048,
      recursive_scanning: false,
      entries
    };
  }

  // Batch operations
  async importMultipleCharacterCards(files: File[]): Promise<{
    successful: Array<{ file: string; data: any }>;
    failed: Array<{ file: string; error: string }>;
  }> {
    const successful: Array<{ file: string; data: any }> = [];
    const failed: Array<{ file: string; error: string }> = [];

    for (const file of files) {
      try {
        const data = await this.importSingleCharacterCard(file);
        successful.push({ file: file.name, data });
      } catch (error) {
        failed.push({ file: file.name, error: error.message });
      }
    }

    return { successful, failed };
  }

  private async importSingleCharacterCard(file: File): Promise<any> {
    const fileName = file.name;
    
    if (fileName.toLowerCase().endsWith('.png')) {
      const arrayBuffer = await file.arrayBuffer();
      const base64Data = extractCharaDataFromPng(arrayBuffer);
      
      if (!base64Data) {
        throw new Error('No character data found in PNG file');
      }
      
      const jsonString = new TextDecoder('utf-8').decode(
        Uint8Array.from(atob(base64Data), c => c.charCodeAt(0))
      );
      
      return JSON.parse(jsonString);
    } else if (fileName.toLowerCase().endsWith('.json') || fileName.toLowerCase().endsWith('.jsonc')) {
      const text = await file.text();
      return JSON.parse(text);
    } else {
      throw new Error('Unsupported file format');
    }
  }

  // Relationship management
  addRelationship(relationship: CharacterRelationship): void {
    const sourceRelationships = this.characterRelationships.get(relationship.sourceCharacterId) || [];
    sourceRelationships.push(relationship);
    this.characterRelationships.set(relationship.sourceCharacterId, sourceRelationships);
  }

  getRelationships(characterId: string): CharacterRelationship[] {
    return this.characterRelationships.get(characterId) || [];
  }

  updateRelationshipStrength(relationshipId: string, newStrength: number, reason: string): void {
    for (const [characterId, relationships] of this.characterRelationships.entries()) {
      const relationship = relationships.find(r => r.id === relationshipId);
      if (relationship) {
        const oldStrength = relationship.strength;
        relationship.strength = Math.max(-100, Math.min(100, newStrength));
        relationship.history.push({
          timestamp: Date.now(),
          event: reason,
          impact: relationship.strength - oldStrength
        });
        break;
      }
    }
  }
}

// Singleton instance
export const sillyTavernService = new SillyTavernService();
