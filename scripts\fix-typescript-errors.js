#!/usr/bin/env node

/**
 * TypeScript错误修复脚本
 * 自动修复常见的TypeScript错误，如未使用的导入、变量等
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class TypeScriptErrorFixer {
  constructor() {
    this.fixedFiles = [];
    this.errors = [];
  }

  /**
   * 运行TypeScript检查并获取错误列表
   */
  getTypeScriptErrors() {
    try {
      execSync('npx tsc --noEmit', { stdio: 'pipe' });
      return [];
    } catch (error) {
      const output = error.stdout.toString();
      return this.parseErrors(output);
    }
  }

  /**
   * 解析TypeScript错误输出
   */
  parseErrors(output) {
    const lines = output.split('\n');
    const errors = [];
    
    for (const line of lines) {
      if (line.includes('error TS6133:') && line.includes('is declared but its value is never read')) {
        const match = line.match(/^(.+?):(\d+):(\d+) - error TS6133: '(.+?)' is declared but its value is never read/);
        if (match) {
          errors.push({
            file: match[1],
            line: parseInt(match[2]),
            column: parseInt(match[3]),
            variable: match[4],
            type: 'unused-variable'
          });
        }
      } else if (line.includes('error TS6192:') && line.includes('All imports in import declaration are unused')) {
        const match = line.match(/^(.+?):(\d+):(\d+) - error TS6192:/);
        if (match) {
          errors.push({
            file: match[1],
            line: parseInt(match[2]),
            column: parseInt(match[3]),
            type: 'unused-import-declaration'
          });
        }
      }
    }
    
    return errors;
  }

  /**
   * 修复未使用的导入
   */
  fixUnusedImports(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      
      // 查找并修复未使用的导入
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        
        // 检查是否是import语句
        if (line.trim().startsWith('import ') && line.includes(' from ')) {
          const importMatch = line.match(/import\s+\{([^}]+)\}\s+from\s+['"]([^'"]+)['"]/);
          if (importMatch) {
            const imports = importMatch[1].split(',').map(imp => imp.trim());
            const usedImports = imports.filter(imp => {
              const importName = imp.split(' as ')[0].trim();
              // 检查是否在文件中使用
              const regex = new RegExp(`\\b${importName}\\b`, 'g');
              const matches = content.match(regex);
              return matches && matches.length > 1; // 大于1因为导入语句本身也会匹配
            });
            
            if (usedImports.length === 0) {
              // 删除整个导入行
              lines[i] = '';
            } else if (usedImports.length < imports.length) {
              // 只保留使用的导入
              lines[i] = `import { ${usedImports.join(', ')} } from '${importMatch[2]}';`;
            }
          }
        }
      }
      
      // 移除空行
      const cleanedContent = lines.filter(line => line.trim() !== '').join('\n');
      
      if (cleanedContent !== content) {
        fs.writeFileSync(filePath, cleanedContent);
        this.fixedFiles.push(filePath);
        console.log(`✅ 修复了文件: ${filePath}`);
      }
    } catch (error) {
      console.error(`❌ 修复文件失败 ${filePath}:`, error.message);
      this.errors.push({ file: filePath, error: error.message });
    }
  }

  /**
   * 修复未使用的变量（添加下划线前缀）
   */
  fixUnusedVariables(filePath, variableName, lineNumber) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      const lines = content.split('\n');
      
      if (lineNumber <= lines.length) {
        const line = lines[lineNumber - 1];
        // 在变量名前添加下划线
        const newLine = line.replace(
          new RegExp(`\\b${variableName}\\b`),
          `_${variableName}`
        );
        
        if (newLine !== line) {
          lines[lineNumber - 1] = newLine;
          fs.writeFileSync(filePath, lines.join('\n'));
          console.log(`✅ 修复未使用变量: ${variableName} in ${filePath}:${lineNumber}`);
        }
      }
    } catch (error) {
      console.error(`❌ 修复变量失败 ${filePath}:`, error.message);
      this.errors.push({ file: filePath, error: error.message });
    }
  }

  /**
   * 获取所有TypeScript文件
   */
  getTypeScriptFiles(dir = '.') {
    const files = [];
    const entries = fs.readdirSync(dir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dir, entry.name);
      
      if (entry.isDirectory() && !['node_modules', 'dist', '.git'].includes(entry.name)) {
        files.push(...this.getTypeScriptFiles(fullPath));
      } else if (entry.isFile() && (entry.name.endsWith('.ts') || entry.name.endsWith('.tsx'))) {
        files.push(fullPath);
      }
    }
    
    return files;
  }

  /**
   * 运行所有修复
   */
  async run() {
    console.log('🔧 开始修复TypeScript错误...\n');
    
    // 1. 获取所有错误
    console.log('📊 分析TypeScript错误...');
    const errors = this.getTypeScriptErrors();
    console.log(`发现 ${errors.length} 个错误\n`);
    
    // 2. 按文件分组错误
    const errorsByFile = {};
    for (const error of errors) {
      if (!errorsByFile[error.file]) {
        errorsByFile[error.file] = [];
      }
      errorsByFile[error.file].push(error);
    }
    
    // 3. 修复每个文件的错误
    for (const [filePath, fileErrors] of Object.entries(errorsByFile)) {
      console.log(`🔧 修复文件: ${filePath}`);
      
      // 先修复未使用的导入
      this.fixUnusedImports(filePath);
      
      // 然后修复未使用的变量
      for (const error of fileErrors) {
        if (error.type === 'unused-variable') {
          this.fixUnusedVariables(filePath, error.variable, error.line);
        }
      }
    }
    
    // 4. 生成报告
    this.generateReport();
  }

  /**
   * 生成修复报告
   */
  generateReport() {
    console.log('\n📋 修复报告:');
    console.log(`✅ 成功修复的文件: ${this.fixedFiles.length}`);
    
    if (this.fixedFiles.length > 0) {
      console.log('\n修复的文件列表:');
      this.fixedFiles.forEach(file => console.log(`  - ${file}`));
    }
    
    if (this.errors.length > 0) {
      console.log(`\n❌ 修复失败的文件: ${this.errors.length}`);
      this.errors.forEach(error => console.log(`  - ${error.file}: ${error.error}`));
    }
    
    console.log('\n🎉 修复完成！建议运行 "npm run build" 验证修复结果。');
  }
}

// 运行修复脚本
if (require.main === module) {
  const fixer = new TypeScriptErrorFixer();
  fixer.run().catch(console.error);
}

module.exports = TypeScriptErrorFixer;
