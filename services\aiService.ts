// Universal AI Service - Replaces geminiService.ts with multi-provider support
import { getAIServiceManager } from './geminiClient';
import { AIMessage, GeminiResponseFormat, AIProviderType } from './aiProviders/types';
import { localConfigService } from './localConfigService';

// Legacy compatibility function for existing code
export async function generateGeminiResponse(
  messages: any[],
  model: string = "gemini-2.5-flash-preview-05-20",
  systemInstruction?: string
): Promise<GeminiResponseFormat> {
  const aiService = getAIServiceManager();
  
  // Convert legacy message format to new format
  const aiMessages: AIMessage[] = [];
  
  if (systemInstruction) {
    aiMessages.push({
      role: 'system',
      content: systemInstruction
    });
  }

  // Convert various message formats to standard format
  for (const msg of messages) {
    if (msg.role && msg.parts?.[0]?.text) {
      // Gemini format
      aiMessages.push({
        role: msg.role === 'model' ? 'assistant' : msg.role,
        content: msg.parts[0].text
      });
    } else if (msg.role && msg.content) {
      // Standard format
      aiMessages.push({
        role: msg.role === 'model' ? 'assistant' : msg.role,
        content: msg.content
      });
    }
  }

  try {
    return await aiService.generateGeminiResponse(aiMessages, {
      temperature: 0.9,
      maxTokens: 8192,
      topP: 0.95,
      topK: 40
    });
  } catch (error) {
    console.error("AI Service error:", error);
    
    // Fallback response
    return {
      dialogue: "抱歉，AI服务暂时不可用。请检查您的API配置或稍后再试。",
      speakerName: "系统",
      speakerType: "narrator",
      sceneImageKeyword: "keep_current",
      choices: ["重试", "检查设置", "继续"],
      storyUpdate: "系统遇到了一些问题...",
      mood: "neutral",
      timeOfDay: "unknown"
    };
  }
}

// Enhanced AI functions for new features
export { getAIServiceManager } from './geminiClient';

export async function generateAIResponse(
  messages: AIMessage[],
  options?: {
    provider?: AIProviderType;
    model?: string;
    temperature?: number;
    maxTokens?: number;
    stream?: boolean;
  }
): Promise<GeminiResponseFormat> {
  const aiService = getAIServiceManager();
  
  // Switch provider if specified
  if (options?.provider) {
    await aiService.switchProvider(options.provider);
  }

  try {
    return await aiService.generateGeminiResponse(messages, {
      temperature: options?.temperature ?? 0.9,
      maxTokens: options?.maxTokens ?? 8192,
      topP: 0.95,
      topK: 40
    });
  } catch (error) {
    console.error("Enhanced AI Service error:", error);
    throw error;
  }
}

export async function* generateAIStreamResponse(
  messages: AIMessage[],
  options?: {
    provider?: AIProviderType;
    model?: string;
    temperature?: number;
    maxTokens?: number;
  }
): AsyncGenerator<{ content: string; isComplete: boolean }, void, unknown> {
  const aiService = getAIServiceManager();
  
  // Switch provider if specified
  if (options?.provider) {
    await aiService.switchProvider(options.provider);
  }

  try {
    yield* aiService.generateStreamResponse(messages, {
      temperature: options?.temperature ?? 0.9,
      maxTokens: options?.maxTokens ?? 8192,
      topP: 0.95,
      topK: 40,
      stream: true
    });
  } catch (error) {
    console.error("Stream AI Service error:", error);
    throw error;
  }
}

// Provider management functions
export async function switchAIProvider(provider: AIProviderType): Promise<boolean> {
  const aiService = getAIServiceManager();
  const success = aiService.switchProvider(provider);
  
  if (success) {
    // Update local config
    localConfigService.updateAIConfig({ provider });
  }
  
  return success;
}

export async function getAvailableProviders(): Promise<{
  providers: Record<AIProviderType, { available: boolean; current: boolean }>;
  models: Record<AIProviderType, any[]>;
}> {
  const aiService = getAIServiceManager();
  
  const [providers, models] = await Promise.all([
    Promise.resolve(aiService.getProviderStatus()),
    aiService.getAllAvailableModels()
  ]);
  
  return { providers, models };
}

export async function validateAIProviders(): Promise<Record<AIProviderType, boolean>> {
  const aiService = getAIServiceManager();
  return await aiService.validateAllProviders();
}

export async function testAIProviderConnection(provider: AIProviderType): Promise<{ success: boolean; responseTime: number; error?: string; modelCount?: number }> {
  const aiService = getAIServiceManager();

  // Get the provider instance
  const providers = aiService.getProviderStatus();

  // If provider is not available, try to recreate it with current config
  if (!providers[provider]?.available) {
    console.log(`Provider ${provider} not available, attempting to recreate...`);

    // Get current configuration
    const config = localConfigService.getConfig();
    let providerConfig: any = null;

    if (provider === 'gemini') {
      const apiKey = config.ai?.geminiApiKey || process.env.GEMINI_API_KEY || process.env.API_KEY;
      if (apiKey) {
        providerConfig = {
          type: 'gemini',
          apiKey,
          defaultModel: config.ai?.geminiDefaultModel || 'gemini-2.5-flash-preview-05-20',
          timeout: config.ai?.timeout || 30000
        };
      }
    } else if (provider === 'openai') {
      const apiKey = config.ai?.openaiApiKey || process.env.OPENAI_API_KEY;
      if (apiKey) {
        providerConfig = {
          type: 'openai',
          apiKey,
          baseUrl: config.ai?.openaiBaseUrl,
          defaultModel: config.ai?.openaiDefaultModel || 'gpt-4o-mini',
          timeout: config.ai?.timeout || 30000
        };
      }
    } else if (provider === 'anthropic') {
      const apiKey = config.ai?.anthropicApiKey || process.env.ANTHROPIC_API_KEY;
      if (apiKey) {
        providerConfig = {
          type: 'anthropic',
          apiKey,
          defaultModel: config.ai?.anthropicDefaultModel || 'claude-3-5-haiku-20241022',
          timeout: config.ai?.timeout || 30000
        };
      }
    } else if (config.customProviders?.[provider]) {
      // Custom provider
      const customProvider = config.customProviders[provider];
      if (customProvider.apiKey && customProvider.url) {
        providerConfig = {
          type: provider,
          apiKey: customProvider.apiKey,
          baseUrl: customProvider.url,
          defaultModel: customProvider.defaultModel,
          timeout: config.ai?.timeout || 30000
        };
      }
    }

    if (providerConfig) {
      console.log(`Updating provider config for ${provider}:`, { ...providerConfig, apiKey: '***' });
      aiService.updateProviderConfig(provider, providerConfig);
    } else {
      return {
        success: false,
        responseTime: 0,
        error: '提供商配置不完整或API密钥缺失'
      };
    }
  }

  // Switch to the provider temporarily for testing
  const currentProvider = aiService.getCurrentProvider()?.getProviderType();
  const switched = aiService.switchProvider(provider);

  if (!switched) {
    return {
      success: false,
      responseTime: 0,
      error: '无法切换到指定提供商'
    };
  }

  try {
    const providerInstance = aiService.getCurrentProvider();
    if (!providerInstance) {
      return {
        success: false,
        responseTime: 0,
        error: '提供商实例不存在'
      };
    }

    const result = await providerInstance.testConnection();

    // Switch back to original provider
    if (currentProvider && currentProvider !== provider) {
      aiService.switchProvider(currentProvider);
    }

    return result;
  } catch (error) {
    // Switch back to original provider on error
    if (currentProvider && currentProvider !== provider) {
      aiService.switchProvider(currentProvider);
    }

    return {
      success: false,
      responseTime: 0,
      error: error.message || '测试连接失败'
    };
  }
}

export function getCurrentAIProvider(): AIProviderType | null {
  const aiService = getAIServiceManager();
  const provider = aiService.getCurrentProvider();
  return provider ? provider.getProviderType() : null;
}

// Configuration management
export function updateAIProviderConfig(
  provider: AIProviderType,
  config: {
    apiKey?: string;
    baseUrl?: string;
    defaultModel?: string;
    timeout?: number;
  }
): void {
  const aiService = getAIServiceManager();

  aiService.updateProviderConfig(provider, {
    type: provider,
    ...config
  });

  // For custom providers, don't update the main AI config
  if (typeof provider === 'string' && provider.startsWith('custom_')) {
    // Custom providers are handled separately in local config
    return;
  }

  // Update local config for built-in providers
  localConfigService.updateAIConfig({
    provider,
    [`${provider}ApiKey`]: config.apiKey,
    [`${provider}BaseUrl`]: config.baseUrl,
    [`${provider}DefaultModel`]: config.defaultModel,
    timeout: config.timeout
  });
}

// Utility functions for message conversion
export function convertToAIMessages(messages: any[]): AIMessage[] {
  return messages.map(msg => {
    if (msg.role && msg.parts?.[0]?.text) {
      // Gemini format
      return {
        role: msg.role === 'model' ? 'assistant' : msg.role,
        content: msg.parts[0].text,
        name: msg.name
      };
    } else if (msg.role && msg.content) {
      // Standard format
      return {
        role: msg.role === 'model' ? 'assistant' : msg.role,
        content: msg.content,
        name: msg.name
      };
    } else {
      // Fallback
      return {
        role: 'user',
        content: String(msg.content || msg.text || msg),
        name: msg.name
      };
    }
  });
}

export function convertFromAIMessages(messages: AIMessage[], format: 'gemini' | 'openai' = 'gemini'): any[] {
  if (format === 'gemini') {
    return messages.map(msg => ({
      role: msg.role === 'assistant' ? 'model' : msg.role,
      parts: [{ text: msg.content }],
      ...(msg.name && { name: msg.name })
    }));
  } else {
    return messages.map(msg => ({
      role: msg.role,
      content: msg.content,
      ...(msg.name && { name: msg.name })
    }));
  }
}

// Health check and diagnostics
export async function checkAIServiceHealth(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  providers: Record<AIProviderType, { status: 'ok' | 'error'; error?: string }>;
  currentProvider: AIProviderType | null;
}> {
  try {
    const [validation, providerStatus] = await Promise.all([
      validateAIProviders(),
      Promise.resolve(getAIServiceManager().getProviderStatus())
    ]);
    
    const providers: Record<string, { status: 'ok' | 'error'; error?: string }> = {};
    let healthyCount = 0;
    
    for (const [provider, isValid] of Object.entries(validation)) {
      providers[provider] = {
        status: isValid ? 'ok' : 'error',
        ...(isValid ? {} : { error: 'Validation failed' })
      };
      if (isValid) healthyCount++;
    }
    
    const totalProviders = Object.keys(providers).length;
    let status: 'healthy' | 'degraded' | 'unhealthy';
    
    if (healthyCount === totalProviders) {
      status = 'healthy';
    } else if (healthyCount > 0) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }
    
    return {
      status,
      providers: providers as Record<AIProviderType, { status: 'ok' | 'error'; error?: string }>,
      currentProvider: getCurrentAIProvider()
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      providers: {} as Record<AIProviderType, { status: 'ok' | 'error'; error?: string }>,
      currentProvider: null
    };
  }
}
