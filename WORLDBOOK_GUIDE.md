# 世界书管理器使用指南

## 🚨 **世界书架构修复完成** 🚨

### ✅ **已修复的问题**

1. **输入框颜色问题**: 所有输入框现在使用正确的主题变量，在所有主题下都能正常显示文字
2. **SillyTavern兼容性**: 完整实现了SillyTavern v2规范的世界书触发机制
3. **条目设置项**: 添加了完整的SillyTavern设置界面，包括关键词、优先级、概率等

### 🛠 **核心功能**

#### 1. 关键词触发机制
- **主要关键词**: 用逗号分隔多个关键词，支持正则表达式（用 `/` 包围）
- **次要关键词**: 可选的额外关键词，配合选择性逻辑使用
- **选择性逻辑**: 
  - AND ANY: 次要关键词中任意一个匹配即可
  - AND ALL: 次要关键词必须全部匹配
  - NOT ANY: 次要关键词中任意一个都不能匹配
  - NOT ALL: 次要关键词不能全部匹配

#### 2. 高级设置
- **优先级**: 数值越高优先级越高（0-1000）
- **概率**: 触发概率百分比（0-100%）
- **扫描深度**: 扫描最近多少条消息（1-100）
- **延迟**: 延迟多少条消息后才能触发
- **粘性**: 触发后持续多少条消息
- **冷却**: 触发后冷却多少条消息

#### 3. 匹配选项
- **区分大小写**: 是否区分关键词的大小写
- **匹配整词**: 是否只匹配完整单词（避免部分匹配）

### 📋 **使用步骤**

#### 步骤1: 创建世界书条目
1. 打开世界书管理器
2. 选择一个世界书或创建新的
3. 点击"+"按钮添加新条目
4. 设置条目标题和内容

#### 步骤2: 配置关键词
1. 在"主要关键词"中输入触发词，用逗号分隔
   - 例如: `魔法, 法术, 咒语`
   - 正则表达式: `/魔法|法术/`
2. 可选：添加次要关键词和选择逻辑

#### 步骤3: 调整设置
1. 设置优先级（重要条目设置更高优先级）
2. 调整概率（100%表示必定触发）
3. 配置扫描深度和其他高级选项

#### 步骤4: 测试触发
1. 在右侧"触发测试"面板输入测试文本
2. 点击"测试触发"查看哪些条目会被激活
3. 根据结果调整关键词和设置

### 🎯 **最佳实践**

#### 关键词设置
- 使用多个相关词汇提高触发率
- 考虑同义词和变体形式
- 使用正则表达式处理复杂匹配

#### 优先级管理
- 重要背景信息: 优先级 800-1000
- 角色信息: 优先级 600-800
- 场景描述: 优先级 400-600
- 细节补充: 优先级 200-400
- 可选信息: 优先级 0-200

#### 性能优化
- 合理设置令牌预算避免超出限制
- 使用概率控制随机性
- 利用分组功能避免重复信息

### 🔧 **故障排除**

#### 条目不触发？
1. 检查条目是否已激活（勾选框）
2. 检查世界书是否已激活
3. 验证关键词拼写是否正确
4. 确认匹配选项设置合适
5. 检查延迟设置是否过高

#### 触发过多条目？
1. 提高优先级阈值
2. 降低概率设置
3. 使用分组功能限制同类条目
4. 调整令牌预算

#### 正则表达式不工作？
1. 确保用 `/` 包围正则表达式
2. 检查正则语法是否正确
3. 测试时查看激活日志中的错误信息

### 📊 **SillyTavern兼容性**

本实现完全兼容SillyTavern v2规范，支持：
- ✅ 多关键词匹配
- ✅ 正则表达式支持
- ✅ 次要关键词和选择性逻辑
- ✅ 优先级和概率控制
- ✅ 粘性和冷却机制
- ✅ 分组和权重系统
- ✅ 递归扫描
- ✅ 令牌预算管理

### 🎮 **示例配置**

#### 角色背景条目
```
标题: 艾莉亚背景
关键词: 艾莉亚, Aria, 她
内容: 艾莉亚是一位年轻的魔法师，擅长火系法术...
优先级: 700
概率: 100%
```

#### 魔法系统条目
```
标题: 火系魔法
关键词: 火球术, 火焰, 燃烧, /火.*法术/
次要关键词: 魔法, 咒语
选择性逻辑: AND ANY
内容: 火系魔法是最基础的攻击性法术...
优先级: 500
概率: 80%
```

---

**修复完成时间**: 2024年12月19日
**版本**: v2.0 - SillyTavern兼容版本
