
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MemoryAble: A Gemini VN</title>
  <link rel="icon" href="./favicon.svg" type="image/svg+xml">
  <script src="https://cdn.tailwindcss.com"></script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Mochiy+Pop+One&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Great+Vibes&display=swap" rel="stylesheet">
  <link href="https://cdn.bootcdn.net/ajax/libs/lxgw-wenkai-screen-webfont/1.7.0/style.min.css" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">

  <script type="module" src="/index.tsx"></script>

  <style>
    :root {
      --header-height: 3.0rem;

      /* Image Placeholder Backgrounds */
      --image-placeholder-bg-light: #333842; /* Dark placeholder for "Twilight" (formerly Light) */
      --image-placeholder-bg-dark: #242933; 

      /* Twilight Theme (Formerly Light Theme - now Dark) */
      --solid-bg-light: rgb(20, 22, 28); 
      --bg-secondary-light: rgba(28, 30, 38, 0.85); 
      --bg-secondary-rgb-light: 28, 30, 38;
      --bg-element-light: rgba(35, 38, 48, 0.8); 
      --bg-element-rgb-light: 35, 38, 48; /* Added RGB version */
      --text-primary-light: #E0E5F0; 
      --text-primary-rgb-light: 224, 229, 240;
      --text-secondary-light: #A8B0C4; 
      --text-accent-light: #A0B2D8; 
      --text-accent-rgb-light: 160, 178, 216;
      --border-color-light: #404858; 
      --border-color-rgb-light: 64, 72, 88;
      --accent-color-light: #8294C8; 
      --accent-color-rgb-light: 130, 148, 200;
      --accent-color-hover-light: #96A8D8; 
      --scrollbar-track-light: rgba(40, 45, 55, 0.35); 
      --scrollbar-thumb-light: #5A6880;
      --scrollbar-thumb-hover-light: #708098;
      --shadow-color-light: rgba(0, 0, 0, 0.3); 
      --ring-color-light: var(--accent-color-light);
      --code-bg-light: #2A2D35;
      --code-text-light: #9CCAF0;
      --blockquote-border-light: var(--accent-color-light);
      --gradient-line-light-start: rgba(130, 148, 200, 0.1);
      --gradient-line-light-mid: rgba(130, 148, 200, 0.4);
      --gradient-line-light-end: rgba(130, 148, 200, 0.1);
      --subtle-glow-light: 0 0 10px rgba(130, 148, 200, 0.15);

      /* Dialogue Bubbles - Twilight */
      --bg-dialogue-player-light-start: rgba(100, 110, 140, 0.8); 
      --bg-dialogue-player-light-end: rgba(80, 90, 120, 0.8);
      --text-dialogue-player-light: #F0F4FA; 
      --bg-dialogue-npc-light-start: rgba(70, 75, 90, 0.8); 
      --bg-dialogue-npc-light-end: rgba(60, 65, 80, 0.8);   
      --text-dialogue-npc-light: #D8E0EC;
      --bg-dialogue-narrator-light-start: rgba(45, 50, 60, 0.75); 
      --bg-dialogue-narrator-light-end: rgba(35, 40, 50, 0.75);   
      --text-dialogue-narrator-light: #B8C0D0;
      
      /* Accent Button - Twilight */
      --accent-button-bg-start-light: rgba(110, 125, 165, 0.9); 
      --accent-button-bg-end-light: rgba(90, 105, 140, 0.9);   
      --accent-button-border-light: #607090;
      --accent-button-inner-shadow-light: rgba(180, 190, 210, 0.15); 
      --accent-button-text-light: #FFFFFF; 
      --accent-button-shadow-color-light: rgba(0, 0, 0, 0.3);

      /* NPC Nameplate - Twilight */
      --npc-nameplate-bg-light-start: rgba(80, 90, 120, 0.95); 
      --npc-nameplate-bg-light-end: rgba(70, 80, 110, 0.95);
      --npc-nameplate-text-light: #F0F4FA;
      --npc-nameplate-border-light: rgba(70, 80, 110, 1);

      /* Notification Twilight Theme */
      --notification-success-bg-gradient-start-light: rgba(60, 90, 70, 0.95); 
      --notification-success-bg-gradient-end-light: rgba(50, 80, 60, 0.9);
      --notification-success-text-light: #C0E8C8;
      --notification-success-border-light: rgba(130, 200, 140, 0.3);
      --notification-success-icon-light: #A0D0B0;
      --notification-error-bg-gradient-start-light: rgba(100, 60, 60, 0.95); 
      --notification-error-bg-gradient-end-light: rgba(90, 50, 50, 0.9);
      --notification-error-text-light: #F8D0D0;
      --notification-error-border-light: rgba(220, 120, 120, 0.3);
      --notification-error-icon-light: #E8A0A0;
      --notification-warning-bg-gradient-start-light: rgba(100, 85, 50, 0.95); 
      --notification-warning-bg-gradient-end-light: rgba(90, 75, 40, 0.9);
      --notification-warning-text-light: #FFE8C0;
      --notification-warning-border-light: rgba(230, 190, 100, 0.4);
      --notification-warning-icon-light: #F0D090;
      --notification-info-bg-gradient-start-light: rgba(60, 80, 110, 0.95); 
      --notification-info-bg-gradient-end-light: rgba(50, 70, 100, 0.9);
      --notification-info-text-light: #D0E0F8;
      --notification-info-border-light: rgba(120, 160, 230, 0.3);
      --notification-info-icon-light: #A0C0E8;
      --notification-achievement-bg-gradient-start-light: rgba(80, 60, 100, 0.95); 
      --notification-achievement-bg-gradient-end-light: rgba(70, 50, 90, 0.9);
      --notification-achievement-text-light: #E8D8F8;
      --notification-achievement-border-light: rgba(170, 130, 210, 0.3);
      --notification-achievement-icon-light: #C8A8E0;
      
      --notification-shadow-light: rgba(0, 0, 0, 0.4); 
      --notification-text-shadow-light: 1px 1px 3px rgba(0, 0, 0, 0.6);
      --notification-border-radius-elegant: 0.6rem; 


      /* Dark Theme (Refined) */
      --bg-primary-dark: transparent; 
      --solid-bg-dark: rgb(22, 26, 34); 
      --bg-secondary-dark: rgba(28, 32, 42, 0.85); 
      --bg-secondary-rgb-dark: 28, 32, 42;
      --bg-element-dark: rgba(35, 40, 52, 0.8);    
      --bg-element-rgb-dark: 35, 40, 52; /* Added RGB version */
      --text-primary-dark: #D8DEE9; 
      --text-primary-rgb-dark: 216, 222, 233;
      --text-secondary-dark: #A7B0C0; 
      --text-accent-dark: #8FBCBB; /* Nord teal */
      --text-accent-rgb-dark: 143, 188, 187;
      --border-color-dark: #434C5E; /* Nord darker grey */
      --border-color-rgb-dark: 67, 76, 94;
      --accent-color-dark: #81A1C1; /* Nord blue */
      --accent-color-rgb-dark: 129, 161, 193;
      --accent-color-hover-dark: #88C0D0; /* Nord lighter blue */
      --scrollbar-track-dark: rgba(46, 52, 64, 0.4); 
      --scrollbar-thumb-dark: #5E81AC; /* Nord darker blue */
      --scrollbar-thumb-hover-dark: var(--accent-color-hover-dark); 
      --shadow-color-dark: rgba(0, 0, 0, 0.35); 
      --ring-color-dark: var(--accent-color-dark);
      --code-bg-dark: #2E3440; /* Nord darkest grey */
      --code-text-dark: #D8DEE9; 
      --blockquote-border-dark: var(--accent-color-dark);
      --gradient-line-dark-start: rgba(129, 161, 193, 0.1);
      --gradient-line-dark-mid: rgba(129, 161, 193, 0.4);
      --gradient-line-dark-end: rgba(129, 161, 193, 0.1);
      --subtle-glow-dark: 0 0 10px rgba(143, 188, 187, 0.15);


      /* Dialogue Bubbles - Dark (Refined) */
      --bg-dialogue-player-dark-start: rgba(94, 129, 172, 0.8); /* Nord blue */
      --bg-dialogue-player-dark-end: rgba(80, 110, 150, 0.8);
      --text-dialogue-player-dark: #ECEFF4; /* Nord lightest grey */
      --bg-dialogue-npc-dark-start: rgba(76, 86, 106, 0.8); /* Nord grey-blue */
      --bg-dialogue-npc-dark-end: rgba(67, 76, 94, 0.8);   
      --text-dialogue-npc-dark: #E5E9F0; /* Nord lighter grey */
      --bg-dialogue-narrator-dark-start: rgba(59, 66, 82, 0.75); /* Nord darker grey */
      --bg-dialogue-narrator-dark-end: rgba(46, 52, 64, 0.75);   
      --text-dialogue-narrator-dark: #B0B8C4;

      /* Accent Button - Dark (Refined) */
      --accent-button-bg-start-dark: rgba(129, 161, 193, 0.9); /* Nord blue */
      --accent-button-bg-end-dark: rgba(94, 129, 172, 0.9);   
      --accent-button-border-dark: #5E81AC; 
      --accent-button-inner-shadow-dark: rgba(216, 222, 233, 0.1); 
      --accent-button-text-dark: #2E3440; /* Nord darkest for contrast */
      --accent-button-shadow-color-dark: rgba(0, 0, 0, 0.4);

      /* NPC Nameplate - Dark (Refined) */
      --npc-nameplate-bg-dark-start: rgba(76, 86, 106, 0.95); 
      --npc-nameplate-bg-dark-end: rgba(67, 76, 94, 0.95);
      --npc-nameplate-text-dark: #ECEFF4; 
      --npc-nameplate-border-dark: rgba(67, 76, 94, 1);

      /* Notification Dark Theme (Refined) */
      --notification-success-bg-gradient-start-dark: rgba(60, 100, 75, 0.95); 
      --notification-success-bg-gradient-end-dark: rgba(45, 85, 60, 0.9);
      --notification-success-text-dark: #A3BE8C; /* Nord green */
      --notification-success-border-dark: rgba(163, 190, 140, 0.4);
      --notification-error-bg-gradient-start-dark: rgba(120, 70, 80, 0.95); 
      --notification-error-bg-gradient-end-dark: rgba(100, 55, 65, 0.9);
      --notification-error-text-dark: #BF616A; /* Nord red */
      --notification-error-border-dark: rgba(191, 97, 106, 0.4);
      --notification-warning-bg-gradient-start-dark: rgba(130, 100, 60, 0.95); 
      --notification-warning-bg-gradient-end-dark: rgba(110, 85, 45, 0.9);
      --notification-warning-text-dark: #EBCB8B; /* Nord yellow */
      --notification-warning-border-dark: rgba(235, 203, 139, 0.4);
      --notification-info-bg-gradient-start-dark: rgba(70, 90, 120, 0.95); 
      --notification-info-bg-gradient-end-dark: rgba(55, 75, 100, 0.9);
      --notification-info-text-dark: #88C0D0; /* Nord light blue */
      --notification-info-border-dark: rgba(136, 192, 208, 0.4);
      --notification-achievement-bg-gradient-start-dark: rgba(100, 80, 120, 0.95); 
      --notification-achievement-bg-gradient-end-dark: rgba(85, 65, 100, 0.9);
      --notification-achievement-text-dark: #B48EAD; /* Nord purple */
      --notification-achievement-border-dark: rgba(180, 142, 173, 0.4);
      
      --notification-shadow-dark: rgba(0,0,0, 0.5); 
      --notification-text-shadow-dark: 1px 1px 3px rgba(0, 0, 0, 0.7);


      /* --- RPG Status Panel Variables (Base - can be overridden by themes) --- */
      --rpg-panel-bg: var(--bg-secondary);
      --rpg-panel-border: var(--border-color);
      --rpg-panel-shadow: var(--shadow-color);
      --rpg-header-bg: var(--bg-element);
      --rpg-header-text: var(--text-accent);
      --rpg-header-border: var(--border-color);
      --rpg-text-primary: var(--text-primary);
      --rpg-text-secondary: var(--text-secondary);
      --rpg-text-accent: var(--text-accent); 
      --rpg-icon-color: var(--text-secondary);
      --rpg-divider-color: var(--border-color);
      
      /* RPG Gauge Variables */
      --rpg-gauge-track-bg: rgba(var(--bg-secondary-rgb), 0.3);
      --rpg-gauge-border: var(--border-color);
      --rpg-gauge-xp-fill: linear-gradient(to right, #EBCB8B, #D08770); /* Nord Yellow to Orange */
      --rpg-gauge-health-fill: linear-gradient(to right, #A3BE8C, #8FBCBB); /* Nord Green to Teal */
      --rpg-gauge-height: 10px;
      
      /* RPG Button Variables */
      --rpg-button-bg: linear-gradient(145deg, var(--bg-element), rgba(var(--bg-secondary-rgb), 0.6));
      --rpg-button-border: var(--border-color);
      --rpg-button-text: var(--text-primary);
      --rpg-button-shadow: var(--shadow-color);
      --rpg-button-hover-bg: linear-gradient(145deg, var(--accent-color), var(--accent-color-hover));
      --rpg-button-hover-text: var(--text-dialogue-player-dark); /* Adjusted for dark themes */
      
      /* RPG Attribute/Skill Slot Variables */
      --rpg-slot-bg: rgba(var(--bg-secondary-rgb), 0.2);
      --rpg-slot-border: var(--border-color);
      --rpg-slot-text: var(--text-secondary);
      --rpg-slot-value-text: var(--text-accent);
      --rpg-allocate-button-bg: var(--accent-button-bg-start);
      --rpg-allocate-button-text: var(--accent-button-text);

      /* Forest Theme Specifics (Dark Version - Moonlit Grove) */
      --solid-bg-forest: rgb(20, 30, 25);
      --bg-element-rgb-forest: 35, 45, 40; /* Added RGB version */
      --border-color-rgb-forest: 50, 65, 55;
      --text-primary-rgb-forest: 210, 220, 215;
      --text-accent-rgb-forest: 130, 160, 140; /* Muted moonlight green */
      --accent-color-rgb-forest: 100, 140, 110; /* Deeper forest green */
      --gradient-line-forest-start: rgba(100, 140, 110, 0.1);
      --gradient-line-forest-mid: rgba(130, 160, 140, 0.3);
      --gradient-line-forest-end: rgba(100, 140, 110, 0.1);
      --subtle-glow-forest: 0 0 10px rgba(130, 160, 140, 0.15);
      --rpg-panel-bg-forest: rgba(28, 40, 35, 0.92); 
      --rpg-panel-border-forest: rgba(60, 80, 70, 1);   
      --rpg-panel-shadow-forest: rgba(0, 0, 0, 0.3);
      --rpg-header-bg-forest: rgba(45, 60, 50, 0.85); 
      --rpg-header-text-forest: #B0C8B8; 
      --rpg-header-border-forest: rgba(60, 80, 70, 0.7);
      --rpg-text-primary-forest: #C8D8D0; 
      --rpg-text-secondary-forest: #98A8A0; 
      --rpg-text-accent-forest: #A0BBA8; /* Lighter green accent */
      --rpg-icon-color-forest: #809888;
      --rpg-divider-color-forest: rgba(60, 80, 70, 0.5);
      --rpg-gauge-track-bg-forest: rgba(35, 50, 40, 0.3);
      --rpg-gauge-border-forest: rgba(60, 80, 70, 0.6);
      --rpg-gauge-xp-fill-forest: linear-gradient(to right, #B2D86D, #90B84D); 
      --rpg-gauge-health-fill-forest: linear-gradient(to right, #AED581, #7CB342); 
      --rpg-button-bg-forest: linear-gradient(to bottom, #506858, #405048); 
      --rpg-button-border-forest: #304038;
      --rpg-button-text-forest: #E0E8E4;
      --rpg-button-shadow-forest: rgba(0, 0, 0, 0.35);
      --rpg-button-hover-bg-forest: linear-gradient(to bottom, #405048, #304038);
      --rpg-button-hover-text-forest: #E8F0EC;
      --rpg-slot-bg-forest: rgba(40, 55, 45, 0.4); 
      --rpg-slot-border-forest: rgba(60, 80, 70, 0.5);
      --rpg-slot-text-forest: #90A098;
      --rpg-slot-value-text-forest: #A8C0B0;
      --rpg-allocate-button-bg-forest: linear-gradient(to bottom, #608068, #507058);
      --rpg-allocate-button-text-forest: #D8E0DC;

      /* Starry Theme Specifics (Refined - Cosmic Night) */
      --solid-bg-starry: rgb(10, 12, 20);
      --bg-element-rgb-starry: 18, 22, 32; /* Added RGB version */
      --border-color-rgb-starry: 40, 48, 64;
      --text-primary-rgb-starry: 232, 235, 240;
      --text-accent-rgb-starry: 160, 180, 210; /* Brighter, clearer blue */
      --accent-color-rgb-starry: 210, 180, 110; /* Refined gold */
      --gradient-line-starry-start: rgba(210, 180, 110, 0.1);
      --gradient-line-starry-mid: rgba(160, 180, 210, 0.3);
      --gradient-line-starry-end: rgba(210, 180, 110, 0.1);
      --subtle-glow-starry: 0 0 10px rgba(210, 180, 110, 0.2);
      --rpg-panel-bg-starry: rgba(20, 25, 38, 0.95);    
      --rpg-panel-border-starry: rgba(180, 150, 90, 0.7); 
      --rpg-panel-shadow-starry: rgba(0, 0, 0, 0.45);
      --rpg-header-bg-starry: rgba(38, 45, 60, 0.85);  
      --rpg-header-text-starry: #F0D8A0;             
      --rpg-header-border-starry: rgba(180, 150, 90, 0.5);
      --rpg-text-primary-starry: #E0E8F4;           
      --rpg-text-secondary-starry: #B0B8C8;         
      --rpg-text-accent-starry: #FFE8B0;            
      --rpg-icon-color-starry: #C8B898;             
      --rpg-divider-color-starry: rgba(180, 150, 90, 0.4);
      --rpg-gauge-track-bg-starry: rgba(15, 18, 28, 0.5);
      --rpg-gauge-border-starry: rgba(180, 150, 90, 0.5);
      --rpg-gauge-xp-fill-starry: linear-gradient(to right, #FFEE88, #FFD850); 
      --rpg-gauge-health-fill-starry: linear-gradient(to right, #A0B8D0, #C0D0E0); 
      --rpg-button-bg-starry: linear-gradient(to bottom, #C8A870, #A88850); 
      --rpg-button-border-starry: #886830;
      --rpg-button-text-starry: #101218;
      --rpg-button-shadow-starry: rgba(60, 40, 15, 0.4);
      --rpg-button-hover-bg-starry: linear-gradient(to bottom, #A88850, #886830);
      --rpg-slot-bg-starry: rgba(30, 40, 55, 0.4);    
      --rpg-slot-border-starry: rgba(180, 150, 90, 0.4);
      --rpg-slot-text-starry: #B0B8C8;
      --rpg-slot-value-text-starry: #F0D8A0;
      --rpg-allocate-button-bg-starry: linear-gradient(to bottom, #E0C888, #C8A870); 
      --rpg-allocate-button-text-starry: #181C22;

      /* Sakura Theme Specifics (Dark Version - Midnight Sakura) */
      --solid-bg-sakura: rgb(25, 20, 28);
      --bg-element-rgb-sakura: 40, 35, 45; /* Added RGB version */
      --border-color-rgb-sakura: 60, 50, 55;
      --text-primary-rgb-sakura: 230, 220, 225;
      --text-accent-rgb-sakura: 200, 170, 185; /* Muted cherry blossom */
      --accent-color-rgb-sakura: 220, 150, 180; /* Deep cherry pink */
      --gradient-line-sakura-start: rgba(220, 150, 180, 0.1);
      --gradient-line-sakura-mid: rgba(200, 170, 185, 0.3);
      --gradient-line-sakura-end: rgba(220, 150, 180, 0.1);
      --subtle-glow-sakura: 0 0 10px rgba(200, 170, 185, 0.15);
      --rpg-panel-bg-sakura: rgba(35, 28, 32, 0.92); 
      --rpg-panel-border-sakura: rgba(190, 150, 170, 1); 
      --rpg-panel-shadow-sakura: rgba(0, 0, 0, 0.3);
      --rpg-header-bg-sakura: rgba(55, 45, 50, 0.85); 
      --rpg-header-text-sakura: #E8D8E0; 
      --rpg-header-border-sakura: rgba(190, 150, 170, 0.7);
      --rpg-text-primary-sakura: #D8C8D0; 
      --rpg-text-secondary-sakura: #B0A0A8; 
      --rpg-text-accent-sakura: #D8B0C0; /* Lighter pink accent */
      --rpg-icon-color-sakura: #C0A8B0;
      --rpg-divider-color-sakura: rgba(190, 150, 170, 0.5);
      --rpg-gauge-track-bg-sakura: rgba(45, 35, 40, 0.3);
      --rpg-gauge-border-sakura: rgba(190, 150, 170, 0.6);
      --rpg-gauge-xp-fill-sakura: linear-gradient(to right, #FFC0CB, #FFB6C1); /* Pinks */
      --rpg-gauge-health-fill-sakura: linear-gradient(to right, #DB7093, #C71585); /* Deeper Pinks */
      --rpg-button-bg-sakura: linear-gradient(to bottom, #8B4513, #A0522D); /* Darker, richer browns like dark wood */
      --rpg-button-border-sakura: #6B2503;
      --rpg-button-text-sakura: #F5E8E0; /* Creamy text */
      --rpg-button-shadow-sakura: rgba(0, 0, 0, 0.35);
      --rpg-button-hover-bg-sakura: linear-gradient(to bottom, #A0522D, #8B4513);
      --rpg-slot-bg-sakura: rgba(50, 40, 45, 0.4);
      --rpg-slot-border-sakura: rgba(190, 150, 170, 0.5);
      --rpg-slot-text-sakura: #B8A8B0;
      --rpg-slot-value-text-sakura: #C8B0B8;
      --rpg-allocate-button-bg-sakura: linear-gradient(to bottom, #E0B0C8, #D8A8B8); 
      --rpg-allocate-button-text-sakura: #302028;

      /* Candy Theme Specifics (Dark Version - Twilight Treats) */
      --solid-bg-candy: rgb(28, 22, 30);
      --bg-element-rgb-candy: 45, 38, 50; /* Added RGB version */
      --border-color-rgb-candy: 65, 55, 60;
      --text-primary-rgb-candy: 225, 218, 230;
      --text-accent-rgb-candy: 190, 175, 210; /* Muted berry/grape */
      --accent-color-rgb-candy: 170, 140, 180; /* Deeper plum/violet */
      --gradient-line-candy-start: rgba(170, 140, 180, 0.1);
      --gradient-line-candy-mid: rgba(190, 175, 210, 0.3);
      --gradient-line-candy-end: rgba(170, 140, 180, 0.1);
      --subtle-glow-candy: 0 0 10px rgba(190, 175, 210, 0.15);
      --rpg-panel-bg-candy: rgba(40, 30, 42, 0.92); 
      --rpg-panel-border-candy: rgba(150, 120, 160, 1); 
      --rpg-panel-shadow-candy: rgba(0, 0, 0, 0.3);
      --rpg-header-bg-candy: rgba(60, 50, 62, 0.85); 
      --rpg-header-text-candy: #E0D8E8; 
      --rpg-header-border-candy: rgba(150, 120, 160, 0.7);
      --rpg-text-primary-candy: #D0C8D8; 
      --rpg-text-secondary-candy: #A898B0; 
      --rpg-text-accent-candy: #C0B0C8; /* Lighter plum accent */
      --rpg-icon-color-candy: #B0A0B8;
      --rpg-divider-color-candy: rgba(150, 120, 160, 0.5);
      --rpg-gauge-track-bg-candy: rgba(50, 40, 52, 0.3);
      --rpg-gauge-border-candy: rgba(150, 120, 160, 0.6);
      --rpg-gauge-xp-fill-candy: linear-gradient(to right, #FFD700, #FFA500); /* Gold to Orange */
      --rpg-gauge-health-fill-candy: linear-gradient(to right, #98FB98, #3CB371); /* PaleGreen to MediumSeaGreen */
      --rpg-button-bg-candy: linear-gradient(to bottom, #7B68EE, #6A5ACD); /* MediumSlateBlue shades */
      --rpg-button-border-candy: #483D8B; /* DarkSlateBlue */
      --rpg-button-text-candy: #F0F8FF; /* AliceBlue */
      --rpg-button-shadow-candy: rgba(0, 0, 0, 0.35);
      --rpg-button-hover-bg-candy: linear-gradient(to bottom, #6A5ACD, #483D8B);
      --rpg-slot-bg-candy: rgba(55, 45, 58, 0.4);
      --rpg-slot-border-candy: rgba(150, 120, 160, 0.5);
      --rpg-slot-text-candy: #B8A8B0;
      --rpg-slot-value-text-candy: #C8B8D0;
      --rpg-allocate-button-bg-candy: linear-gradient(to bottom, #B088C0, #A078B0); 
      --rpg-allocate-button-text-candy: #28202C;


      /* Custom Status Panel Styles (Defaults, overridden by themes) */
      --panel-bg: var(--bg-element);
      --panel-border: var(--border-color);
      --panel-item-bg: rgba(var(--bg-secondary-rgb), 0.2);
      --panel-item-bg-rgb: var(--bg-secondary-rgb); 
      --panel-item-border: rgba(var(--border-color-rgb), 0.3); 
      --panel-section-title-bg: rgba(var(--bg-secondary-rgb), 0.1);
      --panel-section-title-border: var(--border-color);
      --panel-text-primary: var(--text-primary);
      --panel-text-primary-rgb: var(--text-primary-rgb);
      --panel-text-secondary: var(--text-secondary);
      --panel-text-accent: var(--text-accent);
      --panel-text-accent-rgb: var(--text-accent-rgb);
      --panel-highlight-bg: var(--accent-color);
      --panel-highlight-bg-rgb: var(--accent-color-rgb);
      --panel-highlight-text: var(--text-dialogue-player-dark); /* Adjusted for dark themes */
    }
    body {
      font-family: 'Noto Sans SC', 'LXGW WenKai Screen', sans-serif;
      margin: 0;
      padding: 0;
      color: var(--text-primary);
      background-color: transparent; 
      overflow: hidden; 
      min-height: 100vh; 
      -webkit-tap-highlight-color: transparent; 
      overscroll-behavior-y: contain; 
    }
    html.no-image-bg.theme-light body { background-color: var(--solid-bg-light); }
    html.no-image-bg.theme-dark body  { background-color: var(--solid-bg-dark); }
    html.no-image-bg.theme-sakura body{ background-color: var(--solid-bg-sakura); }
    html.no-image-bg.theme-starry body{ background-color: var(--solid-bg-starry); }
    html.no-image-bg.theme-candy body { background-color: var(--solid-bg-candy); }
    html.no-image-bg.theme-forest body{ background-color: var(--solid-bg-forest); }


    *, ::before, ::after { box-sizing: border-box; border-width: 0; border-style: solid; border-color: currentColor; }

    #root {
      width: 100vw;
      height: 100vh;
      overflow: hidden; 
      display: flex; 
      flex-direction: column; 
      overscroll-behavior-y: contain; 
      background-color: transparent; 
    }
    html.no-image-bg.theme-light #root { background-color: var(--solid-bg-light); }
    html.no-image-bg.theme-dark #root  { background-color: var(--solid-bg-dark); }
    html.no-image-bg.theme-sakura #root{ background-color: var(--solid-bg-sakura); }
    html.no-image-bg.theme-starry #root{ background-color: var(--solid-bg-starry); }
    html.no-image-bg.theme-candy #root { background-color: var(--solid-bg-candy); }
    html.no-image-bg.theme-forest #root{ background-color: var(--solid-bg-forest); }


    ::-webkit-scrollbar { width: 8px; height: 8px; }
    ::-webkit-scrollbar-track { background: var(--scrollbar-track, rgba(0,0,0,0.1)); border-radius: 4px; }
    ::-webkit-scrollbar-thumb { background: var(--scrollbar-thumb, #888); border-radius: 4px; }
    ::-webkit-scrollbar-thumb:hover { background: var(--scrollbar-thumb-hover, #555); }
    * { scrollbar-width: thin; scrollbar-color: var(--scrollbar-thumb, #888) var(--scrollbar-track, rgba(0,0,0,0.1)); }

    .app-title-gradient {
      background-image: linear-gradient(45deg, var(--accent-color), var(--text-accent));
      -webkit-background-clip: text;
      background-clip: text;
      color: transparent;
      font-family: 'Mochiy Pop One', 'LXGW WenKai Screen', sans-serif;
    }
    .app-title-enhanced {
      font-family: 'Great Vibes', 'LXGW WenKai Screen', cursive; 
    }

    .rich-text-content { overflow-wrap: break-word; word-break: break-word; }
    .rich-text-content strong, .rich-text-content b { font-weight: bold; }
    .rich-text-content em, .rich-text-content i { font-style: italic; }
    .rich-text-content u { text-decoration: underline; }
    .rich-text-content p { margin-bottom: 0.5em; overflow-wrap: break-word; word-break: break-word; }
    .rich-text-content ul, .rich-text-content ol { margin-left: 1.5em; margin-bottom: 0.5em; }
    .rich-text-content li { margin-bottom: 0.25em; }
    .rich-text-content pre { background-color: var(--code-bg, #f0f0f0); color: var(--code-text, #333); padding: 0.5em; border-radius: 4px; overflow-x: auto; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; margin-bottom: 0.5em; }
    .rich-text-content code { background-color: var(--code-bg, #f0f0f0); color: var(--code-text, #D6336C); padding: 0.1em 0.3em; border-radius: 3px; font-family: 'Courier New', Courier, monospace; font-size: 0.9em; }
    .rich-text-content blockquote { border-left: 4px solid var(--blockquote-border, #ccc); padding-left: 1em; margin-left: 0; margin-right: 0; margin-bottom: 0.5em; font-style: italic; color: var(--text-secondary, #555); }
    .rich-text-content h1, .rich-text-content h2, .rich-text-content h3, .rich-text-content h4, .rich-text-content h5, .rich-text-content h6 { font-weight: bold; margin-top: 0.8em; margin-bottom: 0.4em; }
    .rich-text-content h1 { font-size: 1.8em; } .rich-text-content h2 { font-size: 1.5em; } .rich-text-content h3 { font-size: 1.3em; } .rich-text-content h4 { font-size: 1.1em; }

    /* Performance optimizations */
    .modal-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 200px;
      color: var(--text-primary);
      font-size: 14px;
    }

    .modal-loading::after {
      content: '';
      width: 20px;
      height: 20px;
      margin-left: 8px;
      border: 2px solid var(--text-primary);
      border-top: 2px solid transparent;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Optimize animations for better performance */
    * {
      will-change: auto;
    }

    .animate-fadeIn,
    .animate-spin {
      will-change: transform, opacity;
    }

    /* Reduce motion for users who prefer it */
    @media (prefers-reduced-motion: reduce) {
      * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }
    }
    
    .btn-dreamy { 
      padding: 0.6rem 1.2rem; /* Slightly more padding */
      font-weight: 500; 
      border-radius: 0.375rem; /* Standardized border-radius */
      cursor: pointer; 
      transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1); /* Smoother transition */
      position: relative; 
      overflow: hidden; 
      z-index: 1; 
      border: 1px solid var(--accent-button-border, transparent); 
      background-image: linear-gradient(160deg, var(--accent-button-bg-start), var(--accent-button-bg-end)); 
      color: var(--accent-button-text); 
      box-shadow: 
        0 2px 4px -1px var(--accent-button-shadow-color), /* Softer main shadow */
        inset 0 1px 1px var(--accent-button-inner-shadow), /* Inner highlight */
        inset 0 0 0 1px var(--accent-button-border); /* Inner border for definition */
      text-shadow: 0 1px 1px rgba(0,0,0,0.1); /* Darker text shadow for better readability */
      min-height: 2.5rem; /* Adjusted min-height */
    }
    .btn-dreamy:hover:not(:disabled) { 
      transform: translateY(-2px) scale(1.02); /* More pronounced hover lift */
      box-shadow: 
        0 4px 8px -2px var(--accent-button-shadow-color), /* Enhanced hover shadow */
        inset 0 1px 1px var(--accent-button-inner-shadow), 
        inset 0 0 0 1px var(--accent-button-border);
      filter: brightness(1.1); /* Brighter on hover */
    }
    .btn-dreamy:active:not(:disabled) { 
      transform: translateY(0px) scale(0.98); /* Press down effect */
      box-shadow: 
        0 1px 2px var(--accent-button-shadow-color), /* Reduced shadow on active */
        inset 0 1px 2px rgba(0,0,0,0.15), /* Deeper inset shadow */
        inset 0 0 0 1px var(--accent-button-border); 
      filter: brightness(0.9); /* Darker on active */
    }
    .btn-dreamy:disabled { 
      opacity: 0.55; 
      cursor: not-allowed; 
      transform: none; 
      box-shadow: 0 1px 1px -0.5px var(--accent-button-shadow-color), inset 0 0 0 1px var(--accent-button-border); 
      filter: grayscale(50%); 
    }
    .btn-dreamy-xs { 
      padding: 0.4rem 0.8rem; 
      font-size: 0.8rem; 
      border-radius: 0.3rem; 
      min-height: 2rem; 
    }
    .btn-dreamy-icon { 
      padding: 0.6rem; /* Standardized padding for icon buttons */
      min-width: 2.5rem; 
      min-height: 2.5rem; 
    }


    .resize-handle { position: absolute; bottom: 0; right: 0; width: 16px; height: 16px; cursor: nwse-resize; opacity: 0.3; transition: opacity 0.2s; z-index: 10; }
    .resize-handle:hover { opacity: 0.7; }
    .resize-handle::after { content: ''; position: absolute; bottom: 2px; right: 2px; width: 6px; height: 6px; border-bottom: 2px solid var(--text-secondary); border-right: 2px solid var(--text-secondary); border-top-left-radius: 2px; }

    .shadow-themed { box-shadow: 0 1px 3px 0 var(--shadow-color, rgba(0,0,0,0.1)), 0 1px 2px -1px var(--shadow-color, rgba(0,0,0,0.1)), var(--subtle-glow, none); }
    .shadow-themed-md { box-shadow: 0 4px 6px -1px var(--shadow-color, rgba(0,0,0,0.1)), 0 2px 4px -2px var(--shadow-color, rgba(0,0,0,0.1)), var(--subtle-glow, none); }
    .shadow-themed-lg { box-shadow: 0 10px 15px -3px var(--shadow-color, rgba(0,0,0,0.1)), 0 4px 6px -4px var(--shadow-color, rgba(0,0,0,0.1)), var(--subtle-glow, none); }
    .shadow-themed-xl { box-shadow: 0 20px 25px -5px var(--shadow-color, rgba(0,0,0,0.1)), 0 8px 10px -6px var(--shadow-color, rgba(0,0,0,0.1)), var(--subtle-glow, none); }

    .border-themed { border-color: var(--border-color); }
    .bg-primary-themed { background-color: var(--bg-primary); }
    .bg-secondary-themed { background-color: var(--bg-secondary); }
    .bg-element-themed { background-color: var(--bg-element); }
    .text-primary-themed { color: var(--text-primary); }
    .text-secondary-themed { color: var(--text-secondary); }
    .text-accent-themed { color: var(--text-accent); }
    .ring-accent-themed:focus { outline: 2px solid transparent; outline-offset: 2px; box-shadow: 0 0 0 3px var(--ring-color); }
    .placeholder-themed::placeholder { color: var(--text-secondary); opacity: 0.7; }
    .text-player-themed { color: var(--text-player); }
    .text-narrator-themed { color: var(--text-narrator); }
    
    hr.themed-divider {
      border: none;
      height: 1px;
      background-image: linear-gradient(to right, var(--gradient-line-start, transparent), var(--gradient-line-mid, var(--border-color)), var(--gradient-line-end, transparent));
      margin: 0.75rem 0; 
    }
    .rpg-status-panel hr {
      border: none;
      height: 1px;
      background-image: linear-gradient(to right, 
        rgba(var(--rpg-divider-color-rgb, var(--bg-secondary-rgb)), 0.1), 
        var(--rpg-divider-color), 
        rgba(var(--rpg-divider-color-rgb, var(--bg-secondary-rgb)), 0.1)
      );
      margin: 0.5rem 0;
    }


    .theme-light {
      --bg-primary: var(--bg-primary-light);
      --bg-secondary: var(--bg-secondary-light); 
      --bg-secondary-rgb: var(--bg-secondary-rgb-light);
      --bg-element: var(--bg-element-light); 
      --bg-element-rgb: var(--bg-element-rgb-light);
      --text-primary: var(--text-primary-light); 
      --text-primary-rgb: var(--text-primary-rgb-light);
      --text-secondary: var(--text-secondary-light); 
      --text-accent: var(--text-accent-light); 
      --text-accent-rgb: var(--text-accent-rgb-light);
      --border-color: var(--border-color-light); 
      --border-color-rgb: var(--border-color-rgb-light);
      --accent-color: var(--accent-color-light); 
      --accent-color-rgb: var(--accent-color-rgb-light);
      --accent-color-hover: var(--accent-color-hover-light); 
      --scrollbar-track: var(--scrollbar-track-light);
      --scrollbar-thumb: var(--scrollbar-thumb-light);
      --scrollbar-thumb-hover: var(--scrollbar-thumb-hover-light);
      --shadow-color: var(--shadow-color-light); 
      --ring-color: var(--ring-color-light);
      --code-bg: var(--code-bg-light);
      --code-text: var(--code-text-light);
      --blockquote-border: var(--blockquote-border-light);
      --gradient-line-start: var(--gradient-line-light-start);
      --gradient-line-mid: var(--gradient-line-light-mid);
      --gradient-line-end: var(--gradient-line-light-end);
      --subtle-glow: var(--subtle-glow-light);
      --dialogue-player-bg: linear-gradient(180deg, var(--bg-dialogue-player-light-start), var(--bg-dialogue-player-light-end));
      --dialogue-player-text: var(--text-dialogue-player-light);
      --dialogue-npc-bg: linear-gradient(180deg, var(--bg-dialogue-npc-light-start), var(--bg-dialogue-npc-light-end));
      --dialogue-npc-text: var(--text-dialogue-npc-light);
      --dialogue-narrator-bg: linear-gradient(180deg, var(--bg-dialogue-narrator-light-start), var(--bg-dialogue-narrator-light-end));
      --dialogue-narrator-text: var(--text-dialogue-narrator-light);
      --accent-button-bg-start: var(--accent-button-bg-start-light);
      --accent-button-bg-end: var(--accent-button-bg-end-light);
      --accent-button-border: var(--accent-button-border-light);
      --accent-button-inner-shadow: var(--accent-button-inner-shadow-light);
      --accent-button-text: var(--accent-button-text-light);
      --accent-button-shadow-color: var(--accent-button-shadow-color-light);
      --npc-nameplate-bg: linear-gradient(180deg, var(--npc-nameplate-bg-light-start), var(--npc-nameplate-bg-light-end));
      --npc-nameplate-text: var(--npc-nameplate-text-light);
      --npc-nameplate-border: var(--npc-nameplate-border-light);
      /* Notifications */
      --notification-success-bg-gradient-start: var(--notification-success-bg-gradient-start-light);
      --notification-success-bg-gradient-end: var(--notification-success-bg-gradient-end-light);
      --notification-success-text: var(--notification-success-text-light);
      --notification-success-border: var(--notification-success-border-light);
      --notification-success-icon: var(--notification-success-icon-light);
      --notification-error-bg-gradient-start: var(--notification-error-bg-gradient-start-light);
      --notification-error-bg-gradient-end: var(--notification-error-bg-gradient-end-light);
      --notification-error-text: var(--notification-error-text-light);
      --notification-error-border: var(--notification-error-border-light);
      --notification-error-icon: var(--notification-error-icon-light);
      --notification-warning-bg-gradient-start: var(--notification-warning-bg-gradient-start-light);
      --notification-warning-bg-gradient-end: var(--notification-warning-bg-gradient-end-light);
      --notification-warning-text: var(--notification-warning-text-light);
      --notification-warning-border: var(--notification-warning-border-light);
      --notification-warning-icon: var(--notification-warning-icon-light);
      --notification-info-bg-gradient-start: var(--notification-info-bg-gradient-start-light);
      --notification-info-bg-gradient-end: var(--notification-info-bg-gradient-end-light);
      --notification-info-text: var(--notification-info-text-light);
      --notification-info-border: var(--notification-info-border-light);
      --notification-info-icon: var(--notification-info-icon-light);
      --notification-achievement-bg-gradient-start: var(--notification-achievement-bg-gradient-start-light);
      --notification-achievement-bg-gradient-end: var(--notification-achievement-bg-gradient-end-light);
      --notification-achievement-text: var(--notification-achievement-text-light);
      --notification-achievement-border: var(--notification-achievement-border-light);
      --notification-achievement-icon: var(--notification-achievement-icon-light);
      --notification-shadow: var(--notification-shadow-light);
      --notification-text-shadow: var(--notification-text-shadow-light);
      --notification-border-radius: var(--notification-border-radius-elegant);

      --rpg-panel-bg-light: rgba(35, 38, 50, 0.95); /* Darker for Twilight */
      --rpg-panel-border-light: rgba(80, 90, 115, 1); 
      --rpg-panel-shadow-light: rgba(0, 0, 0, 0.3);
      --rpg-header-bg-light: rgba(55, 60, 75, 0.85); 
      --rpg-header-text-light: #C0D0E8; 
      --rpg-header-border-light: rgba(80, 90, 115, 0.7);
      --rpg-text-primary-light: #D8E2F0; 
      --rpg-text-secondary-light: #A0ACC0; 
      --rpg-text-accent-light: #B0C4E0; 
      --rpg-icon-color-light: #90A0B8;
      --rpg-divider-color-light: rgba(80, 90, 115, 0.5);
      --rpg-divider-color-rgb-light: 80, 90, 115;
      --rpg-gauge-track-bg-light: rgba(30, 35, 45, 0.4);
      --rpg-gauge-border-light: rgba(80, 90, 115, 0.6);
      --rpg-gauge-xp-fill-light: linear-gradient(to right, #B0C4DE, #87CEFA); /* Light blue to sky blue */
      --rpg-gauge-health-fill-light: linear-gradient(to right, #ADD8E6, #87CEEB); /* Lighter blues */
      --rpg-button-bg-light: linear-gradient(to bottom, #7080A0, #607090); 
      --rpg-button-border-light: #506080;
      --rpg-button-text-light: #FFFFFF;
      --rpg-button-shadow-light: rgba(0,0,0, 0.35);
      --rpg-button-hover-bg-light: linear-gradient(to bottom, #607090, #506080);
      --rpg-button-hover-text-light: #FFFFFF;
      --rpg-slot-bg-light: rgba(45, 50, 65, 0.4); 
      --rpg-slot-border-light: rgba(80, 90, 115, 0.5);
      --rpg-slot-text-light: #90A0B8;
      --rpg-slot-value-text-light: #A8B8D0;
      --rpg-allocate-button-bg-light: linear-gradient(to bottom, #8294C8, #7080B0); 
      --rpg-allocate-button-text-light: #F0F4FA;
      --image-placeholder-bg: var(--image-placeholder-bg-light);
      
      /* Custom Panel Styles - Twilight */
      --panel-bg-light: rgba(40, 45, 55, 0.75);
      --panel-border-light: rgba(64, 72, 88, 0.6);
      --panel-item-bg-light: rgba(28, 30, 38, 0.5);
      --panel-item-bg-rgb-light: var(--bg-secondary-rgb-light);
      --panel-item-border-light: rgba(64, 72, 88, 0.4);
      --panel-section-title-bg-light: rgba(48, 53, 65, 0.6);
      --panel-section-title-border-light: rgba(64, 72, 88, 0.5);
      --panel-text-primary-light: #E0E5F0;
      --panel-text-primary-rgb-light: 224, 229, 240;
      --panel-text-secondary-light: #A8B0C4;
      --panel-text-accent-light: #A0B2D8;
      --panel-text-accent-rgb-light: 160, 178, 216;
      --panel-highlight-bg-light: #8294C8;
      --panel-highlight-bg-rgb-light: 130, 148, 200;
      --panel-highlight-text-light: #1C1E24; /* Darker text for light highlight */
    }
    .theme-light { 
        --rpg-panel-bg: var(--rpg-panel-bg-light);
        --rpg-panel-border: var(--rpg-panel-border-light);
        --rpg-panel-shadow: var(--rpg-panel-shadow-light);
        --rpg-header-bg: var(--rpg-header-bg-light);
        --rpg-header-text: var(--rpg-header-text-light);
        --rpg-header-border: var(--rpg-header-border-light);
        --rpg-text-primary: var(--rpg-text-primary-light);
        --rpg-text-secondary: var(--rpg-text-secondary-light);
        --rpg-text-accent: var(--rpg-text-accent-light);
        --rpg-icon-color: var(--rpg-icon-color-light);
        --rpg-divider-color: var(--rpg-divider-color-light);
        --rpg-divider-color-rgb: var(--rpg-divider-color-rgb-light);
        --rpg-gauge-track-bg: var(--rpg-gauge-track-bg-light);
        --rpg-gauge-border: var(--rpg-gauge-border-light);
        --rpg-gauge-xp-fill: var(--rpg-gauge-xp-fill-light);
        --rpg-gauge-health-fill: var(--rpg-gauge-health-fill-light);
        --rpg-button-bg: var(--rpg-button-bg-light);
        --rpg-button-border: var(--rpg-button-border-light);
        --rpg-button-text: var(--rpg-button-text-light);
        --rpg-button-shadow: var(--rpg-button-shadow-light);
        --rpg-button-hover-bg: var(--rpg-button-hover-bg-light);
        --rpg-button-hover-text: var(--rpg-button-hover-text-light);
        --rpg-slot-bg: var(--rpg-slot-bg-light);
        --rpg-slot-border: var(--rpg-slot-border-light);
        --rpg-slot-text: var(--rpg-slot-text-light);
        --rpg-slot-value-text: var(--rpg-slot-value-text-light);
        --rpg-allocate-button-bg: var(--rpg-allocate-button-bg-light);
        --rpg-allocate-button-text: var(--rpg-allocate-button-text-light);

        /* Custom Panel Styles - Twilight */
        --panel-bg: var(--panel-bg-light);
        --panel-border: var(--panel-border-light);
        --panel-item-bg: var(--panel-item-bg-light);
        --panel-item-bg-rgb: var(--panel-item-bg-rgb-light);
        --panel-item-border: var(--panel-item-border-light);
        --panel-section-title-bg: var(--panel-section-title-bg-light);
        --panel-section-title-border: var(--panel-section-title-border-light);
        --panel-text-primary: var(--panel-text-primary-light);
        --panel-text-primary-rgb: var(--panel-text-primary-rgb-light);
        --panel-text-secondary: var(--panel-text-secondary-light);
        --panel-text-accent: var(--panel-text-accent-light);
        --panel-text-accent-rgb: var(--panel-text-accent-rgb-light);
        --panel-highlight-bg: var(--panel-highlight-bg-light);
        --panel-highlight-bg-rgb: var(--panel-highlight-bg-rgb-light);
        --panel-highlight-text: var(--panel-highlight-text-light);
    }

    .theme-dark {
      --bg-primary: var(--bg-primary-dark);
      --bg-secondary: var(--bg-secondary-dark); 
      --bg-secondary-rgb: var(--bg-secondary-rgb-dark);
      --bg-element: var(--bg-element-dark);  
      --bg-element-rgb: var(--bg-element-rgb-dark);
      --text-primary: var(--text-primary-dark); 
      --text-primary-rgb: var(--text-primary-rgb-dark);
      --text-secondary: var(--text-secondary-dark); 
      --text-accent: var(--text-accent-dark); 
      --text-accent-rgb: var(--text-accent-rgb-dark);
      --border-color: var(--border-color-dark); 
      --border-color-rgb: var(--border-color-rgb-dark);
      --accent-color: var(--accent-color-dark); 
      --accent-color-rgb: var(--accent-color-rgb-dark);
      --accent-color-hover: var(--accent-color-hover-dark); 
      --scrollbar-track: var(--scrollbar-track-dark);
      --scrollbar-thumb: var(--scrollbar-thumb-dark);
      --scrollbar-thumb-hover: var(--scrollbar-thumb-hover-dark);
      --shadow-color: var(--shadow-color-dark); 
      --ring-color: var(--ring-color-dark);
      --code-bg: var(--code-bg-dark);
      --code-text: var(--code-text-dark);
      --blockquote-border: var(--blockquote-border-dark);
      --gradient-line-start: var(--gradient-line-dark-start);
      --gradient-line-mid: var(--gradient-line-dark-mid);
      --gradient-line-end: var(--gradient-line-dark-end);
      --subtle-glow: var(--subtle-glow-dark);
      --dialogue-player-bg: linear-gradient(180deg, var(--bg-dialogue-player-dark-start), var(--bg-dialogue-player-dark-end));
      --dialogue-player-text: var(--text-dialogue-player-dark);
      --dialogue-npc-bg: linear-gradient(180deg, var(--bg-dialogue-npc-dark-start), var(--bg-dialogue-npc-dark-end));
      --dialogue-npc-text: var(--text-dialogue-npc-dark);
      --dialogue-narrator-bg: linear-gradient(180deg, var(--bg-dialogue-narrator-dark-start), var(--bg-dialogue-narrator-dark-end));
      --dialogue-narrator-text: var(--text-dialogue-narrator-dark);
      --accent-button-bg-start: var(--accent-button-bg-start-dark);
      --accent-button-bg-end: var(--accent-button-bg-end-dark);
      --accent-button-border: var(--accent-button-border-dark);
      --accent-button-inner-shadow: var(--accent-button-inner-shadow-dark);
      --accent-button-text: var(--accent-button-text-dark);
      --accent-button-shadow-color: var(--accent-button-shadow-color-dark);
      --npc-nameplate-bg: linear-gradient(180deg, var(--npc-nameplate-bg-dark-start), var(--npc-nameplate-bg-dark-end));
      --npc-nameplate-text: var(--npc-nameplate-text-dark);
      --npc-nameplate-border: var(--npc-nameplate-border-dark);
      /* Notifications */
      --notification-success-bg-gradient-start: var(--notification-success-bg-gradient-start-dark);
      --notification-success-bg-gradient-end: var(--notification-success-bg-gradient-end-dark);
      --notification-success-text: var(--notification-success-text-dark);
      --notification-success-border: var(--notification-success-border-dark);
      --notification-success-icon: var(--notification-success-text-dark); /* Match text color for refined look */
      --notification-error-bg-gradient-start: var(--notification-error-bg-gradient-start-dark);
      --notification-error-bg-gradient-end: var(--notification-error-bg-gradient-end-dark);
      --notification-error-text: var(--notification-error-text-dark);
      --notification-error-border: var(--notification-error-border-dark);
      --notification-error-icon: var(--notification-error-text-dark);
      --notification-warning-bg-gradient-start: var(--notification-warning-bg-gradient-start-dark);
      --notification-warning-bg-gradient-end: var(--notification-warning-bg-gradient-end-dark);
      --notification-warning-text: var(--notification-warning-text-dark);
      --notification-warning-border: var(--notification-warning-border-dark);
      --notification-warning-icon: var(--notification-warning-text-dark);
      --notification-info-bg-gradient-start: var(--notification-info-bg-gradient-start-dark);
      --notification-info-bg-gradient-end: var(--notification-info-bg-gradient-end-dark);
      --notification-info-text: var(--notification-info-text-dark);
      --notification-info-border: var(--notification-info-border-dark);
      --notification-info-icon: var(--notification-info-text-dark);
      --notification-achievement-bg-gradient-start: var(--notification-achievement-bg-gradient-start-dark);
      --notification-achievement-bg-gradient-end: var(--notification-achievement-bg-gradient-end-dark);
      --notification-achievement-text: var(--notification-achievement-text-dark);
      --notification-achievement-border: var(--notification-achievement-border-dark);
      --notification-achievement-icon: var(--notification-achievement-text-dark);
      --notification-shadow: var(--notification-shadow-dark);
      --notification-text-shadow: var(--notification-text-shadow-dark);
      --notification-border-radius: var(--notification-border-radius-elegant);

      /* RPG Panel Specific - Dark Theme (Refined) */
      --rpg-panel-bg-dark: rgba(38, 43, 55, 0.95); 
      --rpg-panel-border-dark: rgba(94, 129, 172, 0.8); 
      --rpg-panel-shadow-dark: rgba(0, 0, 0, 0.4);
      --rpg-header-bg-dark: rgba(60, 68, 82, 0.85); 
      --rpg-header-text-dark: #D8DEE9; 
      --rpg-header-border-dark: rgba(94, 129, 172, 0.7);
      --rpg-text-primary-dark: #ECEFF4; 
      --rpg-text-secondary-dark: #B0BCCD; 
      --rpg-text-accent-dark: #8FBCBB; 
      --rpg-icon-color-dark: #A7B0C0;
      --rpg-divider-color-dark: rgba(94, 129, 172, 0.5);
      --rpg-divider-color-rgb-dark: 94, 129, 172;
      --rpg-gauge-track-bg-dark: rgba(30, 35, 45, 0.45); 
      --rpg-gauge-border-dark: rgba(94, 129, 172, 0.65);
      --rpg-gauge-xp-fill-dark: linear-gradient(to right, #EBCB8B, #D08770); 
      --rpg-gauge-health-fill-dark: linear-gradient(to right, #A3BE8C, #8FBCBB); 
      --rpg-button-bg-dark: linear-gradient(to bottom, #5E81AC, #4C566A); 
      --rpg-button-border-dark: #434C5E;
      --rpg-button-text-dark: #ECEFF4;
      --rpg-button-shadow-dark: rgba(0,0,0,0.45);
      --rpg-button-hover-bg-dark: linear-gradient(to bottom, #4C566A, #434C5E);
      --rpg-button-hover-text-dark: #FFFFFF;
      --rpg-slot-bg-dark: rgba(46, 52, 64, 0.5); 
      --rpg-slot-border-dark: rgba(94, 129, 172, 0.55);
      --rpg-slot-text-dark: #B0BCCD;
      --rpg-slot-value-text-dark: #8FBCBB;
      --rpg-allocate-button-bg-dark: linear-gradient(to bottom, #81A1C1, #5E81AC); 
      --rpg-allocate-button-text-dark: #2E3440;
      --image-placeholder-bg: var(--image-placeholder-bg-dark);

      /* Custom Panel Styles - Dark (Refined) */
      --panel-bg-dark: rgba(40, 46, 58, 0.8);
      --panel-border-dark: rgba(76, 86, 106, 0.6);
      --panel-item-bg-dark: rgba(28, 32, 42, 0.55);
      --panel-item-bg-rgb-dark: var(--bg-secondary-rgb-dark);
      --panel-item-border-dark: rgba(76, 86, 106, 0.4);
      --panel-section-title-bg-dark: rgba(50, 58, 72, 0.65);
      --panel-section-title-border-dark: rgba(76, 86, 106, 0.5);
      --panel-text-primary-dark: #D8DEE9;
      --panel-text-primary-rgb-dark: 216, 222, 233;
      --panel-text-secondary-dark: #A7B0C0;
      --panel-text-accent-dark: #8FBCBB;
      --panel-text-accent-rgb-dark: 143, 188, 187;
      --panel-highlight-bg-dark: #8FBCBB;
      --panel-highlight-bg-rgb-dark: 143, 188, 187;
      --panel-highlight-text-dark: #2E3440; /* Dark text for light highlight */
    }
     .theme-dark { 
        --rpg-panel-bg: var(--rpg-panel-bg-dark);
        --rpg-panel-border: var(--rpg-panel-border-dark);
        --rpg-panel-shadow: var(--rpg-panel-shadow-dark);
        --rpg-header-bg: var(--rpg-header-bg-dark);
        --rpg-header-text: var(--rpg-header-text-dark);
        --rpg-header-border: var(--rpg-header-border-dark);
        --rpg-text-primary: var(--rpg-text-primary-dark);
        --rpg-text-secondary: var(--rpg-text-secondary-dark);
        --rpg-text-accent: var(--rpg-text-accent-dark);
        --rpg-icon-color: var(--rpg-icon-color-dark);
        --rpg-divider-color: var(--rpg-divider-color-dark);
        --rpg-divider-color-rgb: var(--rpg-divider-color-rgb-dark);
        --rpg-gauge-track-bg: var(--rpg-gauge-track-bg-dark);
        --rpg-gauge-border: var(--rpg-gauge-border-dark);
        --rpg-gauge-xp-fill: var(--rpg-gauge-xp-fill-dark);
        --rpg-gauge-health-fill: var(--rpg-gauge-health-fill-dark);
        --rpg-button-bg: var(--rpg-button-bg-dark);
        --rpg-button-border: var(--rpg-button-border-dark);
        --rpg-button-text: var(--rpg-button-text-dark);
        --rpg-button-shadow: var(--rpg-button-shadow-dark);
        --rpg-button-hover-bg: var(--rpg-button-hover-bg-dark);
        --rpg-button-hover-text: var(--rpg-button-hover-text-dark);
        --rpg-slot-bg: var(--rpg-slot-bg-dark);
        --rpg-slot-border: var(--rpg-slot-border-dark);
        --rpg-slot-text: var(--rpg-slot-text-dark);
        --rpg-slot-value-text: var(--rpg-slot-value-text-dark);
        --rpg-allocate-button-bg: var(--rpg-allocate-button-bg-dark);
        --rpg-allocate-button-text: var(--rpg-allocate-button-text-dark);

        /* Custom Panel Styles - Dark (Refined) */
        --panel-bg: var(--panel-bg-dark);
        --panel-border: var(--panel-border-dark);
        --panel-item-bg: var(--panel-item-bg-dark);
        --panel-item-bg-rgb: var(--panel-item-bg-rgb-dark);
        --panel-item-border: var(--panel-item-border-dark);
        --panel-section-title-bg: var(--panel-section-title-bg-dark);
        --panel-section-title-border: var(--panel-section-title-border-dark);
        --panel-text-primary: var(--panel-text-primary-dark);
        --panel-text-primary-rgb: var(--panel-text-primary-rgb-dark);
        --panel-text-secondary: var(--panel-text-secondary-dark);
        --panel-text-accent: var(--panel-text-accent-dark);
        --panel-text-accent-rgb: var(--panel-text-accent-rgb-dark);
        --panel-highlight-bg: var(--panel-highlight-bg-dark);
        --panel-highlight-bg-rgb: var(--panel-highlight-bg-rgb-dark);
        --panel-highlight-text: var(--panel-highlight-text-dark);
    }

    .theme-sakura { /* Dark Version - Midnight Sakura */
        --bg-primary: transparent; 
        --bg-secondary: rgba(30, 25, 35, 0.85); /* Deep plum/indigo base */
        --bg-secondary-rgb: 30, 25, 35;
        --bg-element: rgba(40, 35, 45, 0.8); 
        --bg-element-rgb: var(--bg-element-rgb-sakura);
        --text-primary: #E8E0F0; /* Soft off-white */
        --text-primary-rgb: var(--text-primary-rgb-sakura);
        --text-secondary: #B8B0C8; /* Muted lavender */
        --text-accent: #D8A0C0; /* Muted cherry blossom pink */
        --text-accent-rgb: var(--text-accent-rgb-sakura);
        --border-color: #504058; /* Dark plum border */
        --border-color-rgb: var(--border-color-rgb-sakura);
        --accent-color: #C070A0; /* Deep cherry pink */
        --accent-color-rgb: var(--accent-color-rgb-sakura);
        --accent-color-hover: #D080B0; /* Brighter pink hover */
        --scrollbar-track: rgba(40, 35, 45, 0.4);
        --scrollbar-thumb: #605068; 
        --scrollbar-thumb-hover: #786080;
        --shadow-color: rgba(0, 0, 0, 0.35); 
        --ring-color: var(--accent-color);
        --code-bg: #282030; 
        --code-text: #E0C8D8; 
        --blockquote-border: var(--accent-color); 
        --gradient-line-start: var(--gradient-line-sakura-start);
        --gradient-line-mid: var(--gradient-line-sakura-mid);
        --gradient-line-end: var(--gradient-line-sakura-end);
        --subtle-glow: var(--subtle-glow-sakura);
        --bg-dialogue-player-light-start: rgba(160, 100, 130, 0.8); 
        --bg-dialogue-player-light-end: rgba(140, 80, 110, 0.8);
        --text-dialogue-player-light: #F8F0F4; 
        --bg-dialogue-npc-light-start: rgba(80, 60, 90, 0.8); 
        --bg-dialogue-npc-light-end: rgba(70, 50, 80, 0.8);   
        --text-dialogue-npc-light: #E8E0F0; 
        --bg-dialogue-narrator-light-start: rgba(50, 40, 55, 0.75); 
        --bg-dialogue-narrator-light-end: rgba(40, 30, 45, 0.75);   
        --text-dialogue-narrator-light: #C8C0D0; 
        --accent-button-bg-start-light: rgba(190, 120, 160, 0.9); 
        --accent-button-bg-end-light: rgba(170, 100, 140, 0.9);   
        --accent-button-border-light: #A05080; 
        --accent-button-inner-shadow-light: rgba(240, 220, 230, 0.1);
        --accent-button-text-light: #FFFFFF; 
        --accent-button-shadow-color-light: rgba(0, 0, 0, 0.3);
        --npc-nameplate-bg-light-start: rgba(140, 90, 120, 0.95); 
        --npc-nameplate-bg-light-end: rgba(120, 70, 100, 0.95);
        --npc-nameplate-text-light: #F8F0F4;
        --npc-nameplate-border-light: rgba(120, 70, 100, 1);
        --image-placeholder-bg: #2C2533; /* Dark placeholder for Midnight Sakura */
        --notification-success-bg-gradient-start: rgba(70, 100, 80, 0.95); 
        --notification-success-bg-gradient-end: rgba(60, 90, 70, 0.9);
        --notification-success-text: #C8E8D0; 
        --notification-success-icon: #A8D8B8;
        --notification-error-bg-gradient-start: rgba(110, 70, 80, 0.95); 
        --notification-error-bg-gradient-end: rgba(100, 60, 70, 0.9);
        --notification-error-text: #F0D0D8; 
        --notification-error-icon: #E0B0B8;
        --notification-warning-bg-gradient-start: rgba(110, 90, 60, 0.95); 
        --notification-warning-bg-gradient-end: rgba(100, 80, 50, 0.9);
        --notification-warning-text: #F0E0C8; 
        --notification-warning-icon: #E0C8A8;
        --notification-info-bg-gradient-start: rgba(70, 80, 110, 0.95); 
        --notification-info-bg-gradient-end: rgba(60, 70, 100, 0.9);
        --notification-info-text: #D0D8F0; 
        --notification-info-icon: #B0B8E0;
        --notification-achievement-bg-gradient-start: rgba(90, 70, 110, 0.95); 
        --notification-achievement-bg-gradient-end: rgba(80, 60, 100, 0.9);
        --notification-achievement-text: #E0D0F0; 
        --notification-achievement-icon: #C8B0E0;
        --notification-border: var(--border-color); 
        --notification-text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
        --notification-shadow: var(--shadow-color);
        --notification-border-radius: var(--notification-border-radius-elegant);
        --dialogue-player-bg: linear-gradient(180deg, var(--bg-dialogue-player-light-start), var(--bg-dialogue-player-light-end));
        --dialogue-player-text: var(--text-dialogue-player-light);
        --dialogue-npc-bg: linear-gradient(180deg, var(--bg-dialogue-npc-light-start), var(--bg-dialogue-npc-light-end));
        --dialogue-npc-text: var(--text-dialogue-npc-light);
        --dialogue-narrator-bg: linear-gradient(180deg, var(--bg-dialogue-narrator-light-start), var(--bg-dialogue-narrator-light-end));
        --dialogue-narrator-text: var(--text-dialogue-narrator-light);
        --accent-button-bg-start: var(--accent-button-bg-start-light);
        --accent-button-bg-end: var(--accent-button-bg-end-light);
        --accent-button-border: var(--accent-button-border-light);
        --accent-button-inner-shadow: var(--accent-button-inner-shadow-light);
        --accent-button-text: var(--accent-button-text-light);
        --accent-button-shadow-color: var(--accent-button-shadow-color-light);
        --npc-nameplate-bg: linear-gradient(180deg, var(--npc-nameplate-bg-light-start), var(--npc-nameplate-bg-light-end));
        --npc-nameplate-text: var(--npc-nameplate-text-light);
        --npc-nameplate-border: var(--npc-nameplate-border-light);
        --rpg-panel-bg: var(--rpg-panel-bg-sakura);
        --rpg-panel-border: var(--rpg-panel-border-sakura);
        --rpg-panel-shadow: var(--rpg-panel-shadow-sakura);
        --rpg-header-bg: var(--rpg-header-bg-sakura);
        --rpg-header-text: var(--rpg-header-text-sakura);
        --rpg-header-border: var(--rpg-header-border-sakura);
        --rpg-text-primary: var(--rpg-text-primary-sakura);
        --rpg-text-secondary: var(--rpg-text-secondary-sakura);
        --rpg-text-accent: var(--rpg-text-accent-sakura);
        --rpg-icon-color: var(--rpg-icon-color-sakura);
        --rpg-divider-color: var(--rpg-divider-color-sakura);
        --rpg-divider-color-rgb: 190, 150, 170; 
        --rpg-gauge-track-bg: var(--rpg-gauge-track-bg-sakura);
        --rpg-gauge-border: var(--rpg-gauge-border-sakura);
        --rpg-gauge-xp-fill: var(--rpg-gauge-xp-fill-sakura);
        --rpg-gauge-health-fill: var(--rpg-gauge-health-fill-sakura);
        --rpg-button-bg: var(--rpg-button-bg-sakura);
        --rpg-button-border: var(--rpg-button-border-sakura);
        --rpg-button-text: var(--rpg-button-text-sakura);
        --rpg-button-shadow: var(--rpg-button-shadow-sakura);
        --rpg-button-hover-bg: var(--rpg-button-hover-bg-sakura);
        --rpg-slot-bg: var(--rpg-slot-bg-sakura);
        --rpg-slot-border: var(--rpg-slot-border-sakura);
        --rpg-slot-text: var(--rpg-slot-text-sakura);
        --rpg-slot-value-text: var(--rpg-slot-value-text-sakura);
        --rpg-allocate-button-bg: var(--rpg-allocate-button-bg-sakura);
        --rpg-allocate-button-text: var(--rpg-allocate-button-text-sakura);
        /* Custom Panel Styles - Midnight Sakura */
        --panel-bg: rgba(45, 38, 50, 0.8);
        --panel-border: rgba(80, 70, 85, 0.6);
        --panel-item-bg: rgba(30, 25, 35, 0.5);
        --panel-item-bg-rgb: var(--bg-secondary-rgb-sakura); 
        --panel-item-border: rgba(80, 70, 85, 0.4);
        --panel-section-title-bg: rgba(60, 50, 65, 0.6);
        --panel-section-title-border: rgba(80, 70, 85, 0.5);
        --panel-text-primary: #E8E0F0;
        --panel-text-primary-rgb: var(--text-primary-rgb-sakura);
        --panel-text-secondary: #B8B0C8;
        --panel-text-accent: #D8A0C0;
        --panel-text-accent-rgb: var(--text-accent-rgb-sakura);
        --panel-highlight-bg: #C070A0;
        --panel-highlight-bg-rgb: var(--accent-color-rgb-sakura);
        --panel-highlight-text: #F8F0F4; /* Light text on dark pink highlight */
    }
    .theme-starry { /* Refined - Cosmic Night */
        --bg-primary: transparent;
        --bg-secondary: rgba(12, 15, 25, 0.88); 
        --bg-secondary-rgb: 12, 15, 25;
        --bg-element: rgba(18, 22, 32, 0.82); 
        --bg-element-rgb: var(--bg-element-rgb-starry);
        --text-primary: #F0F4FA; 
        --text-primary-rgb: var(--text-primary-rgb-starry);
        --text-secondary: #B8C0D0; 
        --text-accent: #A0B8D8; /* Brighter, clearer blue */
        --text-accent-rgb: var(--text-accent-rgb-starry);
        --border-color: #303848; 
        --border-color-rgb: var(--border-color-rgb-starry);
        --accent-color: #D8B070; /* Refined gold */
        --accent-color-rgb: var(--accent-color-rgb-starry);
        --accent-color-hover: #E8C080; 
        --scrollbar-track: rgba(18, 22, 32, 0.65);
        --scrollbar-thumb: #404A58; 
        --scrollbar-thumb-hover: #505A68;
        --shadow-color: rgba(0, 0, 0, 0.4); 
        --ring-color: var(--text-accent); 
        --code-bg: #161A22; 
        --code-text: #B0C8E0; 
        --blockquote-border: #404A58; 
        --gradient-line-start: var(--gradient-line-starry-start);
        --gradient-line-mid: var(--gradient-line-starry-mid);
        --gradient-line-end: var(--gradient-line-starry-end);
        --subtle-glow: var(--subtle-glow-starry);
        --bg-dialogue-player-dark-start: rgba(100, 120, 160, 0.8); 
        --bg-dialogue-player-dark-end: rgba(80, 100, 140, 0.8);
        --text-dialogue-player-dark: #F8FAFC; 
        --bg-dialogue-npc-dark-start: rgba(50, 45, 80, 0.82);    
        --bg-dialogue-npc-dark-end: rgba(40, 35, 70, 0.78);     
        --text-dialogue-npc-dark: #E0E8F4; 
        --bg-dialogue-narrator-dark-start: rgba(35, 40, 55, 0.78); 
        --bg-dialogue-narrator-dark-end: rgba(25, 30, 45, 0.72);  
        --text-dialogue-narrator-dark: #B8C4D2; 
        --accent-button-bg-start-dark: rgba(210, 180, 110, 0.9); 
        --accent-button-bg-end-dark: rgba(190, 160, 90, 0.9); 
        --accent-button-border-dark: #B89860; 
        --accent-button-inner-shadow-dark: rgba(240, 220, 180, 0.1); 
        --accent-button-text-dark: #101218; /* Dark text on gold button */
        --accent-button-shadow-color-dark: rgba(60, 40, 15, 0.4); 
        --npc-nameplate-bg-dark-start: rgba(100, 120, 160, 0.95);
        --npc-nameplate-bg-dark-end: rgba(80, 100, 140, 0.9);
        --npc-nameplate-text-dark: #F8FAFC;
        --npc-nameplate-border-dark: rgba(80, 100, 140, 1);
        --image-placeholder-bg: #0A0C14; /* Darker placeholder for Starry */
        --notification-success-bg-gradient-start: rgba(50, 90, 80, 0.95); 
        --notification-success-bg-gradient-end: rgba(40, 80, 70, 0.9);
        --notification-success-text: #B8E0D0; 
        --notification-success-icon: #98D0B8;
        --notification-error-bg-gradient-start: rgba(100, 60, 75, 0.95); 
        --notification-error-bg-gradient-end: rgba(90, 50, 65, 0.9);
        --notification-error-text: #F0C8D8; 
        --notification-error-icon: #E0A8B8;
        --notification-warning-bg-gradient-start: rgba(100, 90, 50, 0.95); 
        --notification-warning-bg-gradient-end: rgba(90, 80, 40, 0.9);
        --notification-warning-text: #F0E8C0; 
        --notification-warning-icon: #E0D8A8;
        --notification-info-bg-gradient-start: rgba(60, 75, 100, 0.95); 
        --notification-info-bg-gradient-end: rgba(50, 65, 90, 0.9);
        --notification-info-text: #C8D8E8; 
        --notification-info-icon: #A8B8D8;
        --notification-achievement-bg-gradient-start: rgba(80, 60, 100, 0.95); 
        --notification-achievement-bg-gradient-end: rgba(70, 50, 90, 0.9);
        --notification-achievement-text: #E0C8F0; 
        --notification-achievement-icon: #C8A8E0;
        --notification-border: var(--border-color); 
        --notification-text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.7);
        --notification-shadow: var(--shadow-color);
        --notification-border-radius: var(--notification-border-radius-elegant);
        --dialogue-player-bg: linear-gradient(180deg, var(--bg-dialogue-player-dark-start), var(--bg-dialogue-player-dark-end));
        --dialogue-player-text: var(--text-dialogue-player-dark);
        --dialogue-npc-bg: linear-gradient(180deg, var(--bg-dialogue-npc-dark-start), var(--bg-dialogue-npc-dark-end));
        --dialogue-npc-text: var(--text-dialogue-npc-dark);
        --dialogue-narrator-bg: linear-gradient(180deg, var(--bg-dialogue-narrator-dark-start), var(--bg-dialogue-narrator-dark-end));
        --dialogue-narrator-text: var(--text-dialogue-narrator-dark);
        --accent-button-bg-start: var(--accent-button-bg-start-dark);
        --accent-button-bg-end: var(--accent-button-bg-end-dark);
        --accent-button-border: var(--accent-button-border-dark);
        --accent-button-inner-shadow: var(--accent-button-inner-shadow-dark);
        --accent-button-text: var(--accent-button-text-dark);
        --accent-button-shadow-color: var(--accent-button-shadow-color-dark);
        --npc-nameplate-bg: linear-gradient(180deg, var(--npc-nameplate-bg-dark-start), var(--npc-nameplate-bg-dark-end));
        --npc-nameplate-text: var(--npc-nameplate-text-dark);
        --npc-nameplate-border: var(--npc-nameplate-border-dark);
        --rpg-panel-bg: var(--rpg-panel-bg-starry);
        --rpg-panel-border: var(--rpg-panel-border-starry);
        --rpg-panel-shadow: var(--rpg-panel-shadow-starry);
        --rpg-header-bg: var(--rpg-header-bg-starry);
        --rpg-header-text: var(--rpg-header-text-starry);
        --rpg-header-border: var(--rpg-header-border-starry);
        --rpg-text-primary: var(--rpg-text-primary-starry);
        --rpg-text-secondary: var(--rpg-text-secondary-starry);
        --rpg-text-accent: var(--rpg-text-accent-starry);
        --rpg-icon-color: var(--rpg-icon-color-starry);
        --rpg-divider-color: var(--rpg-divider-color-starry);
        --rpg-divider-color-rgb: 180, 150, 90; 
        --rpg-gauge-track-bg: var(--rpg-gauge-track-bg-starry);
        --rpg-gauge-border: var(--rpg-gauge-border-starry);
        --rpg-gauge-xp-fill: var(--rpg-gauge-xp-fill-starry);
        --rpg-gauge-health-fill: var(--rpg-gauge-health-fill-starry);
        --rpg-button-bg: var(--rpg-button-bg-starry);
        --rpg-button-border: var(--rpg-button-border-starry);
        --rpg-button-text: var(--rpg-button-text-starry);
        --rpg-button-shadow: var(--rpg-button-shadow-starry);
        --rpg-button-hover-bg: var(--rpg-button-hover-bg-starry);
        --rpg-slot-bg: var(--rpg-slot-bg-starry);
        --rpg-slot-border: var(--rpg-slot-border-starry);
        --rpg-slot-text: var(--rpg-slot-text-starry);
        --rpg-slot-value-text: var(--rpg-slot-value-text-starry);
        --rpg-allocate-button-bg: var(--rpg-allocate-button-bg-starry);
        --rpg-allocate-button-text: var(--rpg-allocate-button-text-starry);
        /* Custom Panel Styles - Cosmic Night */
        --panel-bg: rgba(20, 25, 35, 0.8);
        --panel-border: rgba(50, 58, 72, 0.6);
        --panel-item-bg: rgba(12, 15, 25, 0.5);
        --panel-item-bg-rgb: var(--bg-secondary-rgb-starry);
        --panel-item-border: rgba(50, 58, 72, 0.4);
        --panel-section-title-bg: rgba(28, 32, 42, 0.6);
        --panel-section-title-border: rgba(50, 58, 72, 0.5);
        --panel-text-primary: #F0F4FA;
        --panel-text-primary-rgb: var(--text-primary-rgb-starry);
        --panel-text-secondary: #B8C0D0;
        --panel-text-accent: #A0B8D8;
        --panel-text-accent-rgb: var(--text-accent-rgb-starry);
        --panel-highlight-bg: #D8B070;
        --panel-highlight-bg-rgb: var(--accent-color-rgb-starry);
        --panel-highlight-text: #101218; /* Dark text on gold highlight */
    }
    .theme-candy { /* Dark Version - Twilight Treats */
        --bg-primary: transparent;
        --bg-secondary: rgba(35, 28, 40, 0.85); /* Dark plum/chocolate base */
        --bg-secondary-rgb: 35, 28, 40;
        --bg-element: rgba(45, 38, 50, 0.8); 
        --bg-element-rgb: var(--bg-element-rgb-candy);
        --text-primary: #E8E0F0; /* Creamy off-white */
        --text-primary-rgb: var(--text-primary-rgb-candy);
        --text-secondary: #C0B0C8; /* Light lavender/mint */
        --text-accent: #D8B8E0; /* Muted berry/grape */
        --text-accent-rgb: var(--text-accent-rgb-candy);
        --border-color: #584860; /* Dark berry border */
        --border-color-rgb: var(--border-color-rgb-candy);
        --accent-color: #B088C0; /* Deeper plum/violet */
        --accent-color-rgb: var(--accent-color-rgb-candy);
        --accent-color-hover: #C098D0; /* Brighter plum hover */
        --scrollbar-track: rgba(45, 38, 50, 0.4); 
        --scrollbar-thumb: #685870; 
        --scrollbar-thumb-hover: #807088;
        --shadow-color: rgba(0, 0, 0, 0.35); 
        --ring-color: var(--accent-color); 
        --code-bg: #302838; 
        --code-text: #D8C0E8; 
        --blockquote-border: var(--accent-color); 
        --gradient-line-start: var(--gradient-line-candy-start);
        --gradient-line-mid: var(--gradient-line-candy-mid);
        --gradient-line-end: var(--gradient-line-candy-end);
        --subtle-glow: var(--subtle-glow-candy);
        --bg-dialogue-player-light-start: rgba(150, 110, 170, 0.8); 
        --bg-dialogue-player-light-end: rgba(130, 90, 150, 0.8);
        --text-dialogue-player-light: #F8F0FC; 
        --bg-dialogue-npc-light-start: rgba(100, 120, 100, 0.8); /* Dark teal/mint */
        --bg-dialogue-npc-light-end: rgba(80, 100, 80, 0.8);   
        --text-dialogue-npc-light: #E0F0E8; 
        --bg-dialogue-narrator-light-start: rgba(55, 48, 60, 0.75); 
        --bg-dialogue-narrator-light-end: rgba(45, 38, 50, 0.75);   
        --text-dialogue-narrator-light: #C8C0D0; 
        --accent-button-bg-start-light: rgba(170, 140, 180, 0.9); 
        --accent-button-bg-end-light: rgba(150, 120, 160, 0.9); 
        --accent-button-border-light: #806090; 
        --accent-button-inner-shadow-light: rgba(230, 220, 240, 0.1);
        --accent-button-text-light: #FFFFFF; 
        --accent-button-shadow-color-light: rgba(0, 0, 0, 0.3);
        --npc-nameplate-bg-light-start: rgba(100, 120, 100, 0.95);
        --npc-nameplate-bg-light-end: rgba(80, 100, 80, 0.95);
        --npc-nameplate-text-light: #F0F8F4; 
        --npc-nameplate-border-light: rgba(80, 100, 80, 1);
        --image-placeholder-bg: #282030; /* Dark placeholder for Twilight Treats */
        --notification-success-bg-gradient-start: rgba(70, 100, 80, 0.95); 
        --notification-success-bg-gradient-end: rgba(60, 90, 70, 0.9);
        --notification-success-text: #C8E8D0; 
        --notification-success-icon: #A8D8B8;
        --notification-error-bg-gradient-start: rgba(110, 70, 80, 0.95); 
        --notification-error-bg-gradient-end: rgba(100, 60, 70, 0.9);
        --notification-error-text: #F0D0D8; 
        --notification-error-icon: #E0B0B8;
        --notification-warning-bg-gradient-start: rgba(110, 90, 60, 0.95); 
        --notification-warning-bg-gradient-end: rgba(100, 80, 50, 0.9);
        --notification-warning-text: #F0E0C8; 
        --notification-warning-icon: #E0C8A8;
        --notification-info-bg-gradient-start: rgba(70, 80, 110, 0.95); 
        --notification-info-bg-gradient-end: rgba(60, 70, 100, 0.9);
        --notification-info-text: #D0D8F0; 
        --notification-info-icon: #B0B8E0;
        --notification-achievement-bg-gradient-start: rgba(90, 70, 110, 0.95); 
        --notification-achievement-bg-gradient-end: rgba(80, 60, 100, 0.9);
        --notification-achievement-text: #E0D0F0; 
        --notification-achievement-icon: #C8B0E0;
        --notification-border: var(--border-color); 
        --notification-text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
        --notification-shadow: var(--shadow-color);
        --notification-border-radius: var(--notification-border-radius-elegant);
        --dialogue-player-bg: linear-gradient(180deg, var(--bg-dialogue-player-light-start), var(--bg-dialogue-player-light-end));
        --dialogue-player-text: var(--text-dialogue-player-light);
        --dialogue-npc-bg: linear-gradient(180deg, var(--bg-dialogue-npc-light-start), var(--bg-dialogue-npc-light-end));
        --dialogue-npc-text: var(--text-dialogue-npc-light);
        --dialogue-narrator-bg: linear-gradient(180deg, var(--bg-dialogue-narrator-light-start), var(--bg-dialogue-narrator-light-end));
        --dialogue-narrator-text: var(--text-dialogue-narrator-light);
        --accent-button-bg-start: var(--accent-button-bg-start-light);
        --accent-button-bg-end: var(--accent-button-bg-end-light);
        --accent-button-border: var(--accent-button-border-light);
        --accent-button-inner-shadow: var(--accent-button-inner-shadow-light);
        --accent-button-text: var(--accent-button-text-light);
        --accent-button-shadow-color: var(--accent-button-shadow-color-light);
        --npc-nameplate-bg: linear-gradient(180deg, var(--npc-nameplate-bg-light-start), var(--npc-nameplate-bg-light-end));
        --npc-nameplate-text: var(--npc-nameplate-text-light);
        --npc-nameplate-border: var(--npc-nameplate-border-light);
        --rpg-panel-bg: var(--rpg-panel-bg-candy);
        --rpg-panel-border: var(--rpg-panel-border-candy);
        --rpg-panel-shadow: var(--rpg-panel-shadow-candy);
        --rpg-header-bg: var(--rpg-header-bg-candy);
        --rpg-header-text: var(--rpg-header-text-candy);
        --rpg-header-border: var(--rpg-header-border-candy);
        --rpg-text-primary: var(--rpg-text-primary-candy);
        --rpg-text-secondary: var(--rpg-text-secondary-candy);
        --rpg-text-accent: var(--rpg-text-accent-candy);
        --rpg-icon-color: var(--rpg-icon-color-candy);
        --rpg-divider-color: var(--rpg-divider-color-candy);
        --rpg-divider-color-rgb: 150, 120, 160; 
        --rpg-gauge-track-bg: var(--rpg-gauge-track-bg-candy);
        --rpg-gauge-border: var(--rpg-gauge-border-candy);
        --rpg-gauge-xp-fill: var(--rpg-gauge-xp-fill-candy);
        --rpg-gauge-health-fill: var(--rpg-gauge-health-fill-candy);
        --rpg-button-bg: var(--rpg-button-bg-candy);
        --rpg-button-border: var(--rpg-button-border-candy);
        --rpg-button-text: var(--rpg-button-text-candy);
        --rpg-button-shadow: var(--rpg-button-shadow-candy);
        --rpg-button-hover-bg: var(--rpg-button-hover-bg-candy);
        --rpg-slot-bg: var(--rpg-slot-bg-candy);
        --rpg-slot-border: var(--rpg-slot-border-candy);
        --rpg-slot-text: var(--rpg-slot-text-candy);
        --rpg-slot-value-text: var(--rpg-slot-value-text-candy);
        --rpg-allocate-button-bg: var(--rpg-allocate-button-bg-candy);
        --rpg-allocate-button-text: var(--rpg-allocate-button-text-candy);
        /* Custom Panel Styles - Twilight Treats */
        --panel-bg: rgba(50, 40, 55, 0.8);
        --panel-border: rgba(90, 80, 95, 0.6);
        --panel-item-bg: rgba(35, 28, 40, 0.5);
        --panel-item-bg-rgb: var(--bg-secondary-rgb-candy);
        --panel-item-border: rgba(90, 80, 95, 0.4);
        --panel-section-title-bg: rgba(65, 55, 70, 0.6);
        --panel-section-title-border: rgba(90, 80, 95, 0.5);
        --panel-text-primary: #E8E0F0;
        --panel-text-primary-rgb: var(--text-primary-rgb-candy);
        --panel-text-secondary: #C0B0C8;
        --panel-text-accent: #D8B8E0;
        --panel-text-accent-rgb: var(--text-accent-rgb-candy);
        --panel-highlight-bg: #B088C0;
        --panel-highlight-bg-rgb: var(--accent-color-rgb-candy);
        --panel-highlight-text: #2A2230; /* Darker text for light highlight */
    }
    .theme-forest { /* Dark Version - Moonlit Grove */
        --bg-primary: transparent;
        --bg-secondary: rgba(25, 35, 30, 0.85); /* Deep forest green/brown base */
        --bg-secondary-rgb: 25, 35, 30;
        --bg-element: rgba(35, 45, 40, 0.8); 
        --bg-element-rgb: var(--bg-element-rgb-forest);
        --text-primary: #D8E0D4; /* Pale earthy tones */
        --text-primary-rgb: var(--text-primary-rgb-forest);
        --text-secondary: #A8B8A0; /* Soft moss green */
        --text-accent: #B0C8A8; /* Muted moonlight green */
        --text-accent-rgb: var(--text-accent-rgb-forest);
        --border-color: #485840; /* Dark bark/earth border */
        --border-color-rgb: var(--border-color-rgb-forest);
        --accent-color: #80A078; /* Deeper forest green */
        --accent-color-rgb: var(--accent-color-rgb-forest);
        --accent-color-hover: #90B088; /* Brighter green hover */
        --scrollbar-track: rgba(35, 45, 40, 0.4); 
        --scrollbar-thumb: #586850; 
        --scrollbar-thumb-hover: #687860;
        --shadow-color: rgba(0, 0, 0, 0.35); 
        --ring-color: var(--accent-color); 
        --code-bg: #222820; 
        --code-text: #B8D0B0; 
        --blockquote-border: var(--accent-color); 
        --gradient-line-start: var(--gradient-line-forest-start);
        --gradient-line-mid: var(--gradient-line-forest-mid);
        --gradient-line-end: var(--gradient-line-forest-end);
        --subtle-glow: var(--subtle-glow-forest);
        --bg-dialogue-player-light-start: rgba(100, 120, 90, 0.8); 
        --bg-dialogue-player-light-end: rgba(80, 100, 70, 0.8);
        --text-dialogue-player-light: #F0F4EC; 
        --bg-dialogue-npc-light-start: rgba(60, 75, 65, 0.82); 
        --bg-dialogue-npc-light-end: rgba(50, 65, 55, 0.78);   
        --text-dialogue-npc-light: #D8E4D0; 
        --bg-dialogue-narrator-light-start: rgba(40, 50, 45, 0.75); 
        --bg-dialogue-narrator-light-end: rgba(30, 40, 35, 0.75);  
        --text-dialogue-narrator-light: #B8C0B4; 
        --accent-button-bg-start-light: rgba(110, 140, 100, 0.9); 
        --accent-button-bg-end-light: rgba(90, 120, 80, 0.9); 
        --accent-button-border-light: #607858; 
        --accent-button-inner-shadow-light: rgba(200, 220, 190, 0.1);
        --accent-button-text-light: #FFFFFF; 
        --accent-button-shadow-color-light: rgba(0, 0, 0, 0.3);
        --npc-nameplate-bg-light-start: rgba(80, 100, 70, 0.95);
        --npc-nameplate-bg-light-end: rgba(70, 90, 60, 0.95);
        --npc-nameplate-text-light: #F0F4EC; 
        --npc-nameplate-border-light: rgba(70, 90, 60, 1);
        --image-placeholder-bg: #1E2A24; /* Dark placeholder for Moonlit Grove */
        --notification-success-bg-gradient-start: rgba(70, 100, 80, 0.95); 
        --notification-success-bg-gradient-end: rgba(60, 90, 70, 0.9);
        --notification-success-text: #C8E8D0; 
        --notification-success-icon: #A8D8B8;
        --notification-error-bg-gradient-start: rgba(100, 70, 60, 0.95); 
        --notification-error-bg-gradient-end: rgba(90, 60, 50, 0.9);
        --notification-error-text: #F0D8C8; 
        --notification-error-icon: #E0B8A8;
        --notification-warning-bg-gradient-start: rgba(100, 90, 60, 0.95); 
        --notification-warning-bg-gradient-end: rgba(90, 80, 50, 0.9);
        --notification-warning-text: #F0E8C0; 
        --notification-warning-icon: #E0D8A8;
        --notification-info-bg-gradient-start: rgba(70, 80, 100, 0.95); 
        --notification-info-bg-gradient-end: rgba(60, 70, 90, 0.9);
        --notification-info-text: #D0D8E8; 
        --notification-info-icon: #B0B8D8;
        --notification-achievement-bg-gradient-start: rgba(80, 100, 70, 0.95); 
        --notification-achievement-bg-gradient-end: rgba(70, 90, 60, 0.9);
        --notification-achievement-text: #D8E8C8; 
        --notification-achievement-icon: #B8D8A8;
        --notification-border: var(--border-color); 
        --notification-text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
        --notification-shadow: var(--shadow-color);
        --notification-border-radius: var(--notification-border-radius-elegant);
        --dialogue-player-bg: linear-gradient(180deg, var(--bg-dialogue-player-light-start), var(--bg-dialogue-player-light-end));
        --dialogue-player-text: var(--text-dialogue-player-light);
        --dialogue-npc-bg: linear-gradient(180deg, var(--bg-dialogue-npc-light-start), var(--bg-dialogue-npc-light-end));
        --dialogue-npc-text: var(--text-dialogue-npc-light);
        --dialogue-narrator-bg: linear-gradient(180deg, var(--bg-dialogue-narrator-light-start), var(--bg-dialogue-narrator-light-end));
        --dialogue-narrator-text: var(--text-dialogue-narrator-light);
        --accent-button-bg-start: var(--accent-button-bg-start-light);
        --accent-button-bg-end: var(--accent-button-bg-end-light);
        --accent-button-border: var(--accent-button-border-light);
        --accent-button-inner-shadow: var(--accent-button-inner-shadow-light);
        --accent-button-text: var(--accent-button-text-light);
        --accent-button-shadow-color: var(--accent-button-shadow-color-light);
        --npc-nameplate-bg: linear-gradient(180deg, var(--npc-nameplate-bg-light-start), var(--npc-nameplate-bg-light-end));
        --npc-nameplate-text: var(--npc-nameplate-text-light);
        --npc-nameplate-border: var(--npc-nameplate-border-light);
        --rpg-panel-bg: var(--rpg-panel-bg-forest);
        --rpg-panel-border: var(--rpg-panel-border-forest);
        --rpg-panel-shadow: var(--rpg-panel-shadow-forest);
        --rpg-header-bg: var(--rpg-header-bg-forest);
        --rpg-header-text: var(--rpg-header-text-forest);
        --rpg-header-border: var(--rpg-header-border-forest);
        --rpg-text-primary: var(--rpg-text-primary-forest);
        --rpg-text-secondary: var(--rpg-text-secondary-forest);
        --rpg-text-accent: var(--rpg-text-accent-forest);
        --rpg-icon-color: var(--rpg-icon-color-forest);
        --rpg-divider-color: var(--rpg-divider-color-forest);
        --rpg-divider-color-rgb: 60, 80, 70; 
        --rpg-gauge-track-bg: var(--rpg-gauge-track-bg-forest);
        --rpg-gauge-border: var(--rpg-gauge-border-forest);
        --rpg-gauge-xp-fill: var(--rpg-gauge-xp-fill-forest);
        --rpg-gauge-health-fill: var(--rpg-gauge-health-fill-forest);
        --rpg-button-bg: var(--rpg-button-bg-forest);
        --rpg-button-border: var(--rpg-button-border-forest);
        --rpg-button-text: var(--rpg-button-text-forest);
        --rpg-button-shadow: var(--rpg-button-shadow-forest);
        --rpg-button-hover-bg: var(--rpg-button-hover-bg-forest);
        --rpg-slot-bg: var(--rpg-slot-bg-forest);
        --rpg-slot-border: var(--rpg-slot-border-forest);
        --rpg-slot-text: var(--rpg-slot-text-forest);
        --rpg-slot-value-text: var(--rpg-slot-value-text-forest);
        --rpg-allocate-button-bg: var(--rpg-allocate-button-bg-forest);
        --rpg-allocate-button-text: var(--rpg-allocate-button-text-forest);
        /* Custom Panel Styles - Moonlit Grove */
        --panel-bg: rgba(40, 50, 45, 0.8);
        --panel-border: rgba(72, 88, 64, 0.6);
        --panel-item-bg: rgba(25, 35, 30, 0.5);
        --panel-item-bg-rgb: var(--bg-secondary-rgb-forest);
        --panel-item-border: rgba(72, 88, 64, 0.4);
        --panel-section-title-bg: rgba(50, 60, 55, 0.6);
        --panel-section-title-border: rgba(72, 88, 64, 0.5);
        --panel-text-primary: #D8E0D4;
        --panel-text-primary-rgb: var(--text-primary-rgb-forest);
        --panel-text-secondary: #A8B8A0;
        --panel-text-accent: #B0C8A8;
        --panel-text-accent-rgb: var(--text-accent-rgb-forest);
        --panel-highlight-bg: #80A078;
        --panel-highlight-bg-rgb: var(--accent-color-rgb-forest);
        --panel-highlight-text: #F0F4EC; /* Light text on dark green highlight */
    }

    @keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }
    .animate-fadeIn { animation: fadeIn 0.5s ease-out forwards; }
    @keyframes slideInFromRight { from { transform: translateX(100%); opacity: 0; } to { transform: translateX(0); opacity: 1; } }
    .animate-slideInFromRight { animation: slideInFromRight 0.3s ease-out forwards; }

    @keyframes notifyEnterAnimation {
      from { opacity: 0; transform: translateX(30px) scale(0.95); }
      to { opacity: 1; transform: translateX(0) scale(1); }
    }
    .animate-notifyEnter { animation: notifyEnterAnimation 0.5s ease-out forwards; }

    @keyframes notifyExitAnimation {
      from { opacity: 1; transform: translateY(0) scale(1); }
      to { opacity: 0; transform: translateY(-15px) scale(0.95); }
    }
    .animate-notifyExit { animation: notifyExitAnimation 0.4s ease-in forwards; }

    @keyframes subtlePulseGlow {
      0%, 100% { box-shadow: 0 0 3px 0px var(--rpg-text-accent, var(--text-accent-themed)); }
      50% { box-shadow: 0 0 8px 2px var(--rpg-text-accent, var(--text-accent-themed)); }
    }
    .rpg-button-pulsing-glow {
      animation: subtlePulseGlow 2s infinite ease-in-out;
    }


    .app-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
      position: relative; 
    }

    .app-header {
      height: var(--header-height); 
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 1rem;
      background-color: rgba(var(--bg-secondary-rgb), 0.7); 
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      border-bottom: 1px solid;
      border-image-slice: 1;
      border-image-source: linear-gradient(to right, var(--gradient-line-start, transparent), var(--gradient-line-mid, var(--border-color)), var(--gradient-line-end, transparent));
      color: var(--text-primary);
      z-index: 20; 
      flex-shrink: 0; 
    }
    .menu-button, .action-button {
      padding: 0.5rem;
      border-radius: 0.375rem; /* Slightly smaller radius for consistency */
      color: var(--text-secondary);
      transition: background-color 0.2s, color 0.2s, transform 0.1s; /* Added transform */
      min-width: 2.5rem; 
      min-height: 2.5rem; 
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border: 1px solid transparent; /* Prepare for border on hover */
    }
    .menu-button:hover, .action-button:hover {
      background-color: var(--bg-element);
      color: var(--text-accent);
      transform: translateY(-1px); /* Slight lift on hover */
      border-color: var(--accent-color-rgb); /* Accent border on hover */
    }
     .menu-button:active, .action-button:active {
      transform: translateY(0px); /* Press down effect */
      background-color: rgba(var(--accent-color-rgb), 0.2); /* Subtle accent bg on active */
    }

    .app-title-header {
      font-size: 1.25rem; 
      font-weight: 600; 
      text-align: center;
      flex-grow: 1; 
      overflow: hidden; 
      text-overflow: ellipsis; 
      white-space: nowrap; 
    }
    .header-rename-input {
      flex-grow: 1;
      padding: 0.25rem 0.5rem;
      font-size: 1rem; 
      font-weight: 500;
      background-color: var(--bg-element);
      color: var(--text-primary);
      border: 1px solid var(--border-color);
      border-radius: 0.375rem;
      outline: none;
      box-shadow: 0 0 0 2px transparent;
      transition: border-color 0.2s, box-shadow 0.2s;
    }
    .header-rename-input:focus {
      border-color: var(--accent-color);
      box-shadow: 0 0 0 2px var(--ring-color);
    }
    .header-rename-button {
      padding: 0.3rem;
      border-radius: 0.375rem;
      color: var(--text-secondary);
      background-color: transparent;
      transition: background-color 0.2s, color 0.2s;
    }
    .header-rename-button:hover {
      background-color: var(--bg-element);
      color: var(--text-accent);
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .app-main {
      display: flex;
      flex-grow: 1; 
      overflow: hidden; 
      padding: 0.5rem; 
      gap: 0.5rem; 
    }

    .chat-interface-container {
      flex-grow: 1;
      display: flex; 
      justify-content: center; 
      align-items: stretch;    
      min-width: 0; 
    }
    
    .chat-interface-container > div { 
        height: 100%;    
    }

    .rpg-status-panel {
        background-color: var(--rpg-panel-bg);
        border: 1px solid var(--rpg-panel-border);
        box-shadow: 0 2px 5px var(--rpg-panel-shadow), var(--subtle-glow);
        color: var(--rpg-text-primary);
    }
    
    .rpg-header {
        background-color: var(--rpg-header-bg);
        color: var(--rpg-header-text);
        border-bottom: 1px solid; 
        border-image-slice: 1;
        border-image-source: linear-gradient(to right, 
            rgba(var(--rpg-header-border-rgb, var(--rpg-divider-color-rgb, var(--bg-secondary-rgb))), 0.1), 
            var(--rpg-header-border, var(--rpg-divider-color)), 
            rgba(var(--rpg-header-border-rgb, var(--rpg-divider-color-rgb, var(--bg-secondary-rgb))), 0.1)
        );
        padding: 0.3rem 0.5rem; 
        border-radius: 0.5rem 0.5rem 0 0; 
        display: flex;
        align-items: center;
        font-weight: 600;
        font-size: 0.8rem; 
        position: relative; 
    }
    .rpg-header::after { 
        content: '';
        position: absolute;
        left: 25%; 
        right: 25%;
        bottom: -1px; 
        height: 2px;
        background-image: linear-gradient(to right, 
            var(--gradient-line-start, transparent), 
            var(--rpg-text-accent, var(--text-accent)), 
            var(--gradient-line-end, transparent)
        );
        opacity: 0.7;
    }

    .rpg-header .icon { 
        color: var(--rpg-header-text); 
    }
    .rpg-info-block { margin-top: 0.5rem; margin-bottom: 0.5rem; space-y: 0.25rem;}
    .rpg-info-item { display: flex; align-items: center; font-size: 0.7rem; }
    .rpg-info-icon { color: var(--rpg-icon-color); margin-right: 0.3rem; flex-shrink: 0; }
    .rpg-info-label { color: var(--rpg-text-secondary); margin-right: 0.25rem; }
    .rpg-info-value { color: var(--rpg-text-primary); font-weight: 500; }

    .rpg-gauge-bar-rpg .rpg-gauge-track {
        background-color: var(--rpg-gauge-track-bg) !important;
        border: 1px solid var(--rpg-gauge-border) !important;
        height: var(--rpg-gauge-height) !important;
        border-radius: 3px !important; 
    }
    .rpg-gauge-bar-rpg .rpg-gauge-fill {
        border-radius: 2px !important; 
    }
    .rpg-gauge-bar-rpg .rpg-gauge-fill-xp { background-image: var(--rpg-gauge-xp-fill); }
    .rpg-gauge-bar-rpg .rpg-gauge-fill-health { background-image: var(--rpg-gauge-health-fill); }
    
    .rpg-quest-section .rpg-info-icon { color: var(--rpg-icon-color); }
    .rpg-quest-section .rpg-info-label { color: var(--rpg-text-secondary); }
    .rpg-quest-section .text-primary-themed { color: var(--rpg-text-primary); } 
    .rpg-quest-section .text-secondary-themed { color: var(--rpg-text-secondary); } 

    .rpg-relationships .text-xs { font-size: 0.7rem; }
    .rpg-relationships .text-secondary-themed { color: var(--rpg-text-secondary); }
    .rpg-relationships .text-accent-themed { color: var(--rpg-text-accent); }
    
    .rpg-growth-toggle {
        background-color: var(--rpg-header-bg);
        color: var(--rpg-header-text);
        border: 1px solid var(--rpg-header-border);
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.7rem;
        width: 100%;
        text-align: left;
        position: relative;
    }
    .rpg-growth-toggle::after { 
        content: '';
        position: absolute;
        left: 20%; right: 20%; bottom: -1px; height: 1px;
        background-image: linear-gradient(to right, transparent, var(--rpg-header-text), transparent);
        opacity: 0.5;
    }
    .rpg-growth-toggle:hover { filter: brightness(1.1); }
    .rpg-attribute-slot, .rpg-skill-slot {
        background-color: var(--rpg-slot-bg);
        border: 1px solid var(--rpg-slot-border);
        padding: 0.2rem 0.4rem; 
        border-radius: 0.25rem;
        font-size: 0.65rem; 
    }
    .rpg-attribute-slot .text-primary-themed, .rpg-skill-slot .text-primary-themed { color: var(--rpg-slot-text); }
    .rpg-attribute-slot .text-accent-themed, .rpg-skill-slot .text-accent-themed { color: var(--rpg-slot-value-text); }
    .rpg-attribute-slot .btn-dreamy-xs, .rpg-skill-slot .btn-dreamy-xs {
      background-image: var(--rpg-allocate-button-bg) !important;
      color: var(--rpg-allocate-button-text) !important;
      border: 1px solid var(--rpg-panel-border) !important; 
      padding: 0.25rem 0.4rem !important; 
      font-size: 0.6rem !important;
      min-height: 1.5rem !important; 
    }

    .rpg-action-button-rpg {
        background-image: var(--rpg-button-bg) !important;
        border: 1px solid var(--rpg-button-border) !important;
        color: var(--rpg-button-text) !important;
        padding: 0.4rem !important; 
        border-radius: 0.25rem !important; 
        box-shadow: 0 1px 2px var(--rpg-button-shadow) !important;
        transition: background-image 0.2s, color 0.2s, transform 0.1s;
        min-width: 2rem; 
        min-height: 2rem; 
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    .rpg-action-button-rpg:hover {
        background-image: var(--rpg-button-hover-bg) !important;
        color: var(--rpg-button-hover-text) !important;
        transform: translateY(-1px);
    }
    .rpg-action-button-rpg:active {
        transform: translateY(0px);
        filter: brightness(0.9);
    }
    .rpg-action-button-rpg svg { width: 0.875rem; height: 0.875rem; } 
    .rpg-action-button-rpg span[role="img"] { font-size: 1rem; } 


    @media (orientation: portrait) {
      .app-main { padding: 0.1rem; gap: 0.1rem; }
      .chat-interface-root-portrait { width: 100% !important; max-width: 100% !important; min-width: 0 !important; padding: 0.25rem !important; }
      .chat-interface-root-portrait .resize-handle { display: none !important; }
      .chat-interface-root-portrait .overflow-y-auto { padding-left: 0.1rem !important; padding-right: 0.1rem !important; scrollbar-gutter: stable both-edges; overscroll-behavior-y: contain; }
      .dialogue-bubble-content-portrait { max-width: 100% !important; margin-left: 0.1rem !important; margin-right: 0.1rem !important; }
      .app-header { padding: 0 0.5rem; }
      .header-actions { gap: 0.1rem; }
      .menu-button, .action-button { padding: 0.4rem; min-width: 2.25rem; min-height: 2.25rem; }
       .app-title-header { font-size: 1.0rem; }
      .btn-dreamy-xs { padding: 0.5rem 0.8rem; }
    }

    /* Custom Tagged Choice Styling */
    .rich-text-content choice {
      display: block;
      padding: 0.5rem 0.75rem;
      margin: 0.4rem 0; /* Increased margin for better separation */
      border: 1px solid var(--border-color);
      border-radius: 0.3rem;
      background-color: rgba(var(--bg-element-rgb), 0.35); /* Use RGB for opacity */
      color: var(--text-primary);
      font-size: 0.9em;
      line-height: 1.45;
      cursor: default;
      box-shadow: inset 0 1px 2px rgba(0,0,0,0.05), 0 1px 1px rgba(0,0,0,0.03); /* Subtle shadow */
      transition: background-color 0.2s, border-color 0.2s;
    }
    .rich-text-content choice:hover { /* Slight hover effect for visual feedback, though not clickable */
      background-color: rgba(var(--bg-element-rgb), 0.45);
      border-color: var(--accent-color);
    }
    .rich-text-content choice::before {
      content: attr(letter) ". ";
      font-weight: bold;
      color: var(--text-accent);
      margin-right: 0.5em; /* Increased spacing */
    }
    .rich-text-content choice > p:last-child {
        margin-bottom: 0;
    }
    /* End Custom Tagged Choice Styling */


    .custom-status-panel {
      background-color: var(--panel-bg);
      border: 1px solid var(--panel-border);
      border-radius: 0.5rem;
      padding: 0.75rem;
      color: var(--panel-text-primary);
      font-size: 0.8rem; 
      line-height: 1.5;
    }
    .custom-status-panel p { margin-bottom: 0.3em; }
    .custom-status-panel .panel-section {
      margin-bottom: 0.75rem;
      padding-bottom: 0.5rem;
      border-bottom: 1px solid var(--panel-item-border);
    }
    .custom-status-panel .panel-section:last-child {
      margin-bottom: 0;
      border-bottom: none;
      padding-bottom: 0;
    }
    .custom-status-panel .panel-section-title {
      font-size: 0.9rem;
      font-weight: 600;
      color: var(--panel-text-accent);
      margin-bottom: 0.4rem;
      padding-bottom: 0.2rem;
      border-bottom: 1px solid var(--panel-section-title-border);
    }
    .custom-status-panel .panel-item {
      background-color: var(--panel-item-bg);
      padding: 0.4rem 0.6rem;
      border-radius: 0.25rem;
      border: 1px solid var(--panel-item-border);
      margin-bottom: 0.3rem;
      display: flex;
      flex-direction: column; 
      gap: 0.1rem;
    }
    .custom-status-panel .panel-info { font-weight: 500; }
    .custom-status-panel .panel-level {
      font-size: 1.1em; color: var(--panel-highlight-bg); text-align: center; font-weight: bold; padding: 0.3rem;
    }
    .custom-status-panel .panel-key-value { display: flex; justify-content: space-between; align-items: center; }
    .custom-status-panel .kv-label { color: var(--panel-text-secondary); font-weight: 500; }
    .custom-status-panel .kv-value { color: var(--panel-text-primary); font-weight: 600; }
    .custom-status-panel .panel-funds .kv-label,
    .custom-status-panel .panel-other_points .kv-label { color: var(--panel-text-accent); }
    
    .custom-status-panel .panel-text { color: var(--panel-text-secondary); line-height: 1.6; }
    .custom-status-panel .panel-description {
        font-style: italic; padding: 0.5rem; background-color: rgba(var(--bg-secondary-rgb), 0.1); border-left: 3px solid var(--panel-text-accent);
    }
    .custom-status-panel .panel-suggestion { 
      color: var(--panel-text-accent); font-style: italic; border: 1px dashed var(--panel-item-border); padding: 0.5rem;
    }
    .custom-status-panel .panel-item-entry .item-header { display: flex; justify-content: space-between; align-items: center; font-weight: 600; }
    .custom-status-panel .item-name { color: var(--panel-text-primary); }
    .custom-status-panel .item-quantity { color: var(--panel-text-accent); font-size: 0.9em; }
    .custom-status-panel .item-description { font-size: 0.9em; color: var(--panel-text-secondary); }

    .custom-status-panel .panel-hotelroom .hotelroom-name { font-size: 1.05em; color: var(--panel-text-accent); margin-bottom: 0.2rem; display: block; }
    .custom-status-panel .panel-hotelroom .hotelroom-detail { font-size: 0.9em; margin-bottom: 0.15rem; }
    .custom-status-panel .panel-hotelroom .detail-label { color: var(--panel-text-secondary); font-weight: 500; margin-right: 0.25rem; }
    
    .custom-status-panel .panel-facility .facility-header { display: flex; justify-content: space-between; align-items: baseline; margin-bottom: 0.15rem; }
    .custom-status-panel .facility-name { color: var(--panel-text-primary); font-weight: 600; }
    .custom-status-panel .facility-status-inline { font-size: 0.8em; color: var(--panel-text-secondary); margin-left: 0.25rem; }
    .custom-status-panel .facility-status-main { font-size: 0.85em; padding: 0.1rem 0.3rem; border-radius: 0.2rem; }
    .custom-status-panel .facility-description { font-size: 0.9em; color: var(--panel-text-secondary); }
    .custom-status-panel .facility-status-locked { background-color: rgba(var(--text-accent-rgb), 0.15); color: var(--text-accent); border: 1px solid rgba(var(--text-accent-rgb), 0.3); }
    .custom-status-panel .facility-status-unlocked { background-color: rgba(var(--accent-color-rgb), 0.25); color: var(--accent-color); border: 1px solid rgba(var(--accent-color-rgb), 0.4); }

    .custom-status-panel .panel-upgrade-condition { display: flex; justify-content: space-between; align-items: center; font-size: 0.9em; }
    .custom-status-panel .panel-upgrade-condition .condition-text { color: var(--panel-text-secondary); }
    .custom-status-panel .panel-upgrade-condition .condition-status { font-weight: 500; }
    .custom-status-panel .panel-upgrade-condition.condition-incomplete .condition-status { color: var(--panel-text-accent); }
    .custom-status-panel .panel-upgrade-condition.condition-complete .condition-status { color: var(--panel-highlight-bg); }
    .custom-status-panel .panel-upgrade-condition.condition-inprogress .condition-status { color: var(--panel-text-accent); font-style: italic; } 
    .custom-status-panel .panel-upgrade-condition.condition-complete .condition-text { text-decoration: line-through; opacity: 0.7; }

    .custom-status-panel .panel-plotstatus { display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; cursor: default; }
    .custom-status-panel .plotstatus-icon { font-size: 1.2em; flex-shrink: 0; color: var(--panel-text-accent); }
    .custom-status-panel .plotstatus-content { flex-grow: 1; }
    .custom-status-panel .plotstatus-title { font-weight: 600; color: var(--panel-text-primary); }
    .custom-status-panel .plotstatus-description { font-size: 0.9em; color: var(--panel-text-secondary); margin: 0.1rem 0 0 0; }
    .custom-status-panel .plotstatus-cost { font-size: 0.85em; color: var(--panel-text-accent); font-weight: 500; flex-shrink: 0; }
    
    .custom-status-panel .panel-game-numerical { padding: 0.5rem; }
    .custom-status-panel .game-numerical-label { font-size: 0.9em; color: var(--panel-text-secondary); margin-bottom: 0.2rem; display: block; }
    .custom-status-panel .game-numerical-bar-container { height: 1.25rem; background-color: rgba(var(--panel-text-primary-rgb), 0.1); border-radius: 0.25rem; border: 1px solid var(--panel-item-border); position: relative; overflow: hidden; }
    .custom-status-panel .game-numerical-bar-fill { height: 100%; background-color: var(--panel-highlight-bg); border-radius: 0.2rem; transition: width 0.5s ease-out; }
    .custom-status-panel .game-numerical-bar-text { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 0.75em; font-weight: 600; color: var(--panel-highlight-text); text-shadow: 1px 1px 1px rgba(0,0,0,0.2); }
    .custom-status-panel .game-numerical-bar-fill[style*="width: 0%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 1%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 2%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 3%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 4%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 5%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 6%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 7%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 8%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 9%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 10%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 11%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 12%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 13%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 14%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 15%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 16%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 17%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 18%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 19%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 20%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 21%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 22%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 23%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 24%"] + .game-numerical-bar-text,
    .custom-status-panel .game-numerical-bar-fill[style*="width: 25%"] + .game-numerical-bar-text {
        color: var(--panel-text-secondary); 
        text-shadow: none;
    }

    .custom-status-panel .info-label { font-weight: 500; color: var(--panel-text-secondary); }
    .custom-status-panel .info-separator { margin: 0 0.25em; color: var(--panel-text-secondary); opacity: 0.7; }
    .custom-status-panel .info-secondary { color: var(--panel-text-secondary); }
    
    /* New BBS Styles */
    .custom-status-panel .panel-bbs-post {
      margin-bottom: 0.5rem;
      padding: 0.5rem;
      background-color: rgba(var(--panel-item-bg-rgb, var(--bg-secondary-rgb)), 0.5); 
      border-left: 3px solid var(--panel-text-accent);
      border-radius: 0.2rem; 
    }
    .custom-status-panel .bbs-post-header {
      display: flex;
      justify-content: space-between;
      align-items: baseline;
      margin-bottom: 0.25rem;
    }
    .custom-status-panel .bbs-post-author {
      font-weight: 600;
      color: var(--panel-text-primary);
      font-size: 0.9em;
    }
    .custom-status-panel .bbs-post-heat {
      font-size: 0.9em;
      color: var(--panel-text-accent);
    }
    .custom-status-panel .bbs-post-content {
      font-size: 0.85em;
      color: var(--panel-text-secondary);
      margin-bottom: 0.25rem;
      white-space: pre-wrap; 
    }
    .custom-status-panel .bbs-post-replies {
      font-size: 0.75em;
      color: var(--panel-text-secondary);
      opacity: 0.8;
    }
    .custom-status-panel .panel-bbs-comment { 
      background-color: transparent; 
      border-color: transparent; 
      font-style: italic;
      color: var(--panel-text-secondary);
      padding: 0.3rem 0.1rem; 
    }
    .custom-status-panel .panel-bbs-comment .bbs-comment-speaker {
      color: var(--panel-text-accent);
      font-weight: 600;
      font-style: normal; 
    }
    .custom-status-panel .panel-section-content > p:first-child { 
        font-weight: 500;
        color: var(--panel-text-secondary);
        margin-bottom: 0.4rem;
    }

  </style>
<link rel="stylesheet" href="/index.css">
</head>
<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root"></div>
  <script type="module" src="./index.tsx"></script>
<script type="module" src="/index.tsx"></script>
</body>
</html>