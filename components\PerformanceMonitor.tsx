import React, { useEffect, useState } from 'react';
import { debug, perf } from '../utils/debug';

interface PerformanceMetrics {
  memoryUsage: number;
  renderTime: number;
  componentCount: number;
  lastUpdate: number;
}

interface PerformanceMonitorProps {
  enabled?: boolean;
  interval?: number;
}

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({ 
  enabled = process.env.NODE_ENV === 'development',
  interval = 5000 
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    memoryUsage: 0,
    renderTime: 0,
    componentCount: 0,
    lastUpdate: Date.now()
  });

  useEffect(() => {
    if (!enabled) return;

    const measurePerformance = () => {
      perf.mark('performance-check-start');
      
      // Memory usage (if available)
      const memoryUsage = (performance as any).memory?.usedJSHeapSize || 0;
      
      // Component count (approximate)
      const componentCount = document.querySelectorAll('[data-reactroot], [data-react-component]').length;
      
      // Render time measurement
      const renderStart = performance.now();
      requestAnimationFrame(() => {
        const renderTime = performance.now() - renderStart;
        
        setMetrics({
          memoryUsage: Math.round(memoryUsage / 1024 / 1024), // MB
          renderTime: Math.round(renderTime * 100) / 100, // ms
          componentCount,
          lastUpdate: Date.now()
        });
        
        perf.mark('performance-check-end');
        perf.measure('performance-check', 'performance-check-start', 'performance-check-end');
      });
    };

    // Initial measurement
    measurePerformance();
    
    // Set up interval
    const intervalId = setInterval(measurePerformance, interval);
    
    return () => clearInterval(intervalId);
  }, [enabled, interval]);

  // Log warnings for performance issues
  useEffect(() => {
    if (!enabled) return;
    
    if (metrics.memoryUsage > 100) {
      debug.warn(`High memory usage detected: ${metrics.memoryUsage}MB`);
    }
    
    if (metrics.renderTime > 16) {
      debug.warn(`Slow render detected: ${metrics.renderTime}ms (target: <16ms for 60fps)`);
    }
  }, [metrics, enabled]);

  if (!enabled) return null;

  return (
    <div 
      className="fixed bottom-4 left-4 bg-black/80 text-white text-xs p-2 rounded font-mono z-[9999]"
      style={{ fontSize: '10px', lineHeight: '1.2' }}
    >
      <div>Memory: {metrics.memoryUsage}MB</div>
      <div>Render: {metrics.renderTime}ms</div>
      <div>Components: {metrics.componentCount}</div>
      <div>Updated: {new Date(metrics.lastUpdate).toLocaleTimeString()}</div>
    </div>
  );
};

// Hook for component-level performance monitoring
export const usePerformanceMonitor = (componentName: string) => {
  useEffect(() => {
    perf.mark(`${componentName}-mount-start`);
    
    return () => {
      perf.mark(`${componentName}-unmount`);
      perf.measure(`${componentName}-lifecycle`, `${componentName}-mount-start`, `${componentName}-unmount`);
    };
  }, [componentName]);

  const measureRender = (renderName: string = 'render') => {
    const startMark = `${componentName}-${renderName}-start`;
    const endMark = `${componentName}-${renderName}-end`;
    
    perf.mark(startMark);
    
    return () => {
      perf.mark(endMark);
      perf.measure(`${componentName}-${renderName}`, startMark, endMark);
    };
  };

  return { measureRender };
};

export default PerformanceMonitor;
