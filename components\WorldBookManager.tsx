import React, { useState, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { CustomNarrativePrimaryElement, CustomNarrativeSubElement } from '../types';
import { Icons, UIText } from '../constants';
import { worldBookTriggerService, WorldBookScanOptions } from '../services/worldBookTriggerService';

interface WorldBookManagerProps {
  worldBookElements: CustomNarrativePrimaryElement[];
  onUpdateElements: (elements: CustomNarrativePrimaryElement[]) => void;
  isOpen: boolean;
  onClose: () => void;
  enableBackdropBlur?: boolean;
}

const WorldBookManager: React.FC<WorldBookManagerProps> = ({
  worldBookElements,
  onUpdateElements,
  isOpen,
  onClose,
  enableBackdropBlur = true
}) => {
  const [selectedElement, setSelectedElement] = useState<string | null>(null);
  const [testText, setTestText] = useState('');
  const [testResults, setTestResults] = useState<any>(null);
  const [scanOptions, setScanOptions] = useState<WorldBookScanOptions>({
    scanDepth: 4,
    tokenBudget: 2048,
    recursiveScanning: false,
    caseSensitive: false,
    matchWholeWords: true,
    includeNames: true,
    minActivations: 0,
    maxDepth: 100,
    maxRecursionSteps: 0
  });

  const handleTestTriggers = useCallback(() => {
    if (!testText.trim()) return;

    const result = worldBookTriggerService.scanForTriggers(
      testText,
      worldBookElements,
      scanOptions,
      0 // message count
    );

    setTestResults(result);
  }, [testText, worldBookElements, scanOptions]);

  const handleUpdateSubElement = useCallback((
    primaryId: string,
    subElementId: string,
    updates: Partial<CustomNarrativeSubElement>
  ) => {
    const updatedElements = worldBookElements.map(element => {
      if (element.id === primaryId) {
        return {
          ...element,
          subElements: element.subElements.map(subElement =>
            subElement.id === subElementId
              ? { ...subElement, ...updates }
              : subElement
          )
        };
      }
      return element;
    });
    onUpdateElements(updatedElements);
  }, [worldBookElements, onUpdateElements]);

  const handleUpdatePrimaryElement = useCallback((
    primaryId: string,
    updates: Partial<CustomNarrativePrimaryElement>
  ) => {
    const updatedElements = worldBookElements.map(element =>
      element.id === primaryId
        ? { ...element, ...updates }
        : element
    );
    onUpdateElements(updatedElements);
  }, [worldBookElements, onUpdateElements]);

  const handleAddNewEntry = useCallback((primaryId: string) => {
    const newEntry: CustomNarrativeSubElement = {
      id: `entry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      key: '新条目',
      value: '',
      isActive: true,
      sillyTavernData: {
        keys: ['新条目'],
        priority: 0,
        probability: 100,
        depth: 4,
        insertion_position: 'after_scenario',
        case_sensitive: false,
        match_whole_words: true,
        sticky: 0,
        cooldown: 0,
        delay: 0,
        group: '',
        group_weight: 1,
        keysecondary: [],
        selectiveLogic: 0
      }
    };

    const updatedElements = worldBookElements.map(element => {
      if (element.id === primaryId) {
        return {
          ...element,
          subElements: [...element.subElements, newEntry]
        };
      }
      return element;
    });
    onUpdateElements(updatedElements);
  }, [worldBookElements, onUpdateElements]);

  const handleDeleteEntry = useCallback((primaryId: string, entryId: string) => {
    const updatedElements = worldBookElements.map(element => {
      if (element.id === primaryId) {
        return {
          ...element,
          subElements: element.subElements.filter(sub => sub.id !== entryId)
        };
      }
      return element;
    });
    onUpdateElements(updatedElements);
  }, [worldBookElements, onUpdateElements]);

  const renderSillyTavernMetadata = (subElement: CustomNarrativeSubElement, primaryId: string) => {
    const sillyData = subElement.sillyTavernData;

    const updateSillyData = (updates: Partial<typeof sillyData>) => {
      handleUpdateSubElement(primaryId, subElement.id, {
        sillyTavernData: { ...sillyData, ...updates }
      });
    };

    return (
      <div className="mt-2 p-3 bg-element-themed/30 rounded-md text-xs">
        <div className="font-semibold text-accent-themed mb-2">SillyTavern 设置:</div>

        {/* 关键词设置 */}
        <div className="space-y-2 mb-3">
          <label className="block">
            <span className="text-secondary-themed">主要关键词 (逗号分隔):</span>
            <input
              type="text"
              value={sillyData?.keys?.join(', ') || ''}
              onChange={(e) => updateSillyData({
                keys: e.target.value.split(',').map(k => k.trim()).filter(k => k)
              })}
              className="w-full mt-1 px-2 py-1 bg-element-themed border border-themed/30 rounded text-primary-themed text-xs focus:ring-1 focus:ring-accent-themed placeholder-themed"
              placeholder="关键词1, 关键词2, /正则表达式/"
            />
          </label>

          <label className="block">
            <span className="text-secondary-themed">次要关键词 (逗号分隔):</span>
            <input
              type="text"
              value={sillyData?.keysecondary?.join(', ') || ''}
              onChange={(e) => updateSillyData({
                keysecondary: e.target.value.split(',').map(k => k.trim()).filter(k => k)
              })}
              className="w-full mt-1 px-2 py-1 bg-element-themed border border-themed/30 rounded text-primary-themed text-xs focus:ring-1 focus:ring-accent-themed placeholder-themed"
              placeholder="次要关键词1, 次要关键词2"
            />
          </label>

          {sillyData?.keysecondary && sillyData.keysecondary.length > 0 && (
            <label className="block">
              <span className="text-secondary-themed">次要关键词逻辑:</span>
              <select
                value={sillyData?.selectiveLogic || 0}
                onChange={(e) => updateSillyData({ selectiveLogic: parseInt(e.target.value) || 0 })}
                className="w-full mt-1 px-2 py-1 bg-element-themed border border-themed/30 rounded text-primary-themed text-xs focus:ring-1 focus:ring-accent-themed"
              >
                <option value={0}>AND ANY (任意一个)</option>
                <option value={1}>AND ALL (全部)</option>
                <option value={2}>NOT ANY (非任意)</option>
                <option value={3}>NOT ALL (非全部)</option>
              </select>
            </label>
          )}
        </div>

        {/* 基础设置 */}
        <div className="grid grid-cols-2 gap-2 mb-3">
          <label className="block">
            <span className="text-secondary-themed">优先级:</span>
            <input
              type="number"
              value={sillyData?.priority || 0}
              onChange={(e) => updateSillyData({ priority: parseInt(e.target.value) || 0 })}
              className="w-full mt-1 px-2 py-1 bg-element-themed border border-themed/30 rounded text-primary-themed text-xs focus:ring-1 focus:ring-accent-themed"
              min="0"
              max="1000"
            />
          </label>

          <label className="block">
            <span className="text-secondary-themed">概率 (%):</span>
            <input
              type="number"
              value={sillyData?.probability || 100}
              onChange={(e) => updateSillyData({ probability: parseInt(e.target.value) || 100 })}
              className="w-full mt-1 px-2 py-1 bg-element-themed border border-themed/30 rounded text-primary-themed text-xs focus:ring-1 focus:ring-accent-themed"
              min="0"
              max="100"
            />
          </label>
        </div>

        {/* 高级设置 */}
        <div className="grid grid-cols-2 gap-2 mb-3">
          <label className="block">
            <span className="text-secondary-themed">深度:</span>
            <input
              type="number"
              value={sillyData?.depth || 4}
              onChange={(e) => updateSillyData({ depth: parseInt(e.target.value) || 4 })}
              className="w-full mt-1 px-2 py-1 bg-element-themed border border-themed/30 rounded text-primary-themed text-xs focus:ring-1 focus:ring-accent-themed"
              min="0"
              max="100"
            />
          </label>

          <label className="block">
            <span className="text-secondary-themed">插入位置:</span>
            <select
              value={sillyData?.insertion_position || 'after_scenario'}
              onChange={(e) => updateSillyData({ insertion_position: e.target.value })}
              className="w-full mt-1 px-2 py-1 bg-element-themed border border-themed/30 rounded text-primary-themed text-xs focus:ring-1 focus:ring-accent-themed"
            >
              <option value="after_scenario">场景后</option>
              <option value="before_char">角色前</option>
              <option value="after_char">角色后</option>
              <option value="top">顶部</option>
              <option value="bottom">底部</option>
            </select>
          </label>
        </div>

        {/* 时间设置 */}
        <div className="grid grid-cols-3 gap-2 mb-3">
          <label className="block">
            <span className="text-secondary-themed">粘性:</span>
            <input
              type="number"
              value={sillyData?.sticky || 0}
              onChange={(e) => updateSillyData({ sticky: parseInt(e.target.value) || 0 })}
              className="w-full mt-1 px-2 py-1 bg-element-themed border border-themed/30 rounded text-primary-themed text-xs focus:ring-1 focus:ring-accent-themed"
              min="0"
              max="100"
            />
          </label>

          <label className="block">
            <span className="text-secondary-themed">冷却:</span>
            <input
              type="number"
              value={sillyData?.cooldown || 0}
              onChange={(e) => updateSillyData({ cooldown: parseInt(e.target.value) || 0 })}
              className="w-full mt-1 px-2 py-1 bg-element-themed border border-themed/30 rounded text-primary-themed text-xs focus:ring-1 focus:ring-accent-themed"
              min="0"
              max="100"
            />
          </label>

          <label className="block">
            <span className="text-secondary-themed">延迟:</span>
            <input
              type="number"
              value={sillyData?.delay || 0}
              onChange={(e) => updateSillyData({ delay: parseInt(e.target.value) || 0 })}
              className="w-full mt-1 px-2 py-1 bg-element-themed border border-themed/30 rounded text-primary-themed text-xs focus:ring-1 focus:ring-accent-themed"
              min="0"
              max="100"
            />
          </label>
        </div>

        {/* 分组设置 */}
        <div className="grid grid-cols-2 gap-2 mb-3">
          <label className="block">
            <span className="text-secondary-themed">分组:</span>
            <input
              type="text"
              value={sillyData?.group || ''}
              onChange={(e) => updateSillyData({ group: e.target.value })}
              className="w-full mt-1 px-2 py-1 bg-element-themed border border-themed/30 rounded text-primary-themed text-xs focus:ring-1 focus:ring-accent-themed placeholder-themed"
              placeholder="分组名称"
            />
          </label>

          <label className="block">
            <span className="text-secondary-themed">分组权重:</span>
            <input
              type="number"
              value={sillyData?.group_weight || 1}
              onChange={(e) => updateSillyData({ group_weight: parseInt(e.target.value) || 1 })}
              className="w-full mt-1 px-2 py-1 bg-element-themed border border-themed/30 rounded text-primary-themed text-xs focus:ring-1 focus:ring-accent-themed"
              min="1"
              max="100"
            />
          </label>
        </div>

        {/* 匹配选项 */}
        <div className="flex flex-wrap gap-3 text-xs">
          <label className="flex items-center gap-1">
            <input
              type="checkbox"
              checked={sillyData?.case_sensitive || false}
              onChange={(e) => updateSillyData({ case_sensitive: e.target.checked })}
              className="w-3 h-3 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed"
            />
            <span className="text-secondary-themed">区分大小写</span>
          </label>

          <label className="flex items-center gap-1">
            <input
              type="checkbox"
              checked={sillyData?.match_whole_words !== false}
              onChange={(e) => updateSillyData({ match_whole_words: e.target.checked })}
              className="w-3 h-3 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed"
            />
            <span className="text-secondary-themed">匹配整词</span>
          </label>
        </div>
      </div>
    );
  };

  const renderElementMetadata = (element: CustomNarrativePrimaryElement) => {
    const metadata = element.metadata;
    if (!metadata) return null;

    return (
      <div className="mb-3 p-3 bg-element-themed/20 rounded-lg text-xs">
        <div className="font-semibold text-accent-themed mb-2">导入元数据:</div>
        <div className="text-secondary-themed space-y-1">
          {metadata.sourceType && <div>来源类型: {metadata.sourceType}</div>}
          {metadata.sourceFileName && <div>文件名: {metadata.sourceFileName}</div>}
          {metadata.characterName && <div>角色名: {metadata.characterName}</div>}
          {metadata.importDate && <div>导入时间: {new Date(metadata.importDate).toLocaleDateString('zh-CN')}</div>}
          {metadata.scan_depth !== undefined && <div>扫描深度: {metadata.scan_depth}</div>}
          {metadata.token_budget !== undefined && <div>令牌预算: {metadata.token_budget}</div>}
          {metadata.recursive_scanning !== undefined && <div>递归扫描: {metadata.recursive_scanning ? '是' : '否'}</div>}
        </div>
      </div>
    );
  };

  if (!isOpen) return null;

  const mainBgClasses = "fixed inset-0 bg-black/60 flex items-center justify-center z-[70] p-4";
  const mainBgBlurClass = enableBackdropBlur ? "backdrop-blur-sm" : "";

  const modalContent = (
    <div
      className={`${mainBgClasses} ${mainBgBlurClass}`}
      onClick={onClose}
      role="dialog"
      aria-modal="true"
      aria-labelledby="worldbook-manager-title"
    >
      <div
        className="bg-secondary-themed p-6 rounded-xl shadow-themed-xl border border-themed flex flex-col w-full max-w-5xl h-[80vh] max-h-[700px] min-h-[500px] mx-4"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center mb-4 flex-shrink-0">
          <h3 id="worldbook-manager-title" className="text-lg font-semibold text-accent-themed flex items-center">
            <Icons.BookOpen className="w-5 h-5 mr-2" />
            世界书管理器
          </h3>
          <button
            onClick={onClose}
            className="p-1 text-primary-themed hover:text-accent-themed rounded-md hover:bg-element-themed/50 transition-colors"
            aria-label="关闭"
          >
            <Icons.Close className="w-5 h-5" />
          </button>
        </div>

        <div className="flex flex-1 gap-4 overflow-hidden">
          {/* Left Panel - World Book List */}
          <div className="w-1/3 flex flex-col min-w-0">
            <h4 className="text-sm font-semibold text-accent-themed mb-2 flex-shrink-0">世界书列表</h4>
            <div className="flex-1 overflow-y-auto space-y-2 pr-2">
              {worldBookElements.map(element => (
                <div
                  key={element.id}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                    selectedElement === element.id
                      ? 'bg-accent-themed/20 border-accent-themed'
                      : 'bg-element-themed/30 border-themed/30 hover:bg-element-themed/50'
                  }`}
                  onClick={() => setSelectedElement(element.id)}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-primary-themed truncate flex-1 mr-2">
                      {element.name}
                    </span>
                    <div className="flex items-center gap-2 flex-shrink-0">
                      <span className="text-xs text-secondary-themed bg-element-themed/50 px-2 py-1 rounded">
                        {element.subElements.length}条
                      </span>
                      <input
                        type="checkbox"
                        checked={element.isActive}
                        onChange={(e) => handleUpdatePrimaryElement(element.id, { isActive: e.target.checked })}
                        className="w-4 h-4 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed"
                        onClick={(e) => e.stopPropagation()}
                      />
                    </div>
                  </div>
                  {element.metadata?.sourceType && (
                    <div className="text-xs text-secondary-themed">
                      来源: {element.metadata.sourceType} • {element.metadata.characterName}
                    </div>
                  )}
                </div>
              ))}
              {worldBookElements.length === 0 && (
                <div className="text-center text-secondary-themed text-sm py-8">
                  暂无世界书数据
                </div>
              )}
            </div>
          </div>

          {/* Middle Panel - Entry Details */}
          <div className="w-1/3 flex flex-col min-w-0">
            <div className="flex items-center justify-between mb-2 flex-shrink-0">
              <h4 className="text-sm font-semibold text-accent-themed">条目详情</h4>
              {selectedElement && (
                <button
                  onClick={() => handleAddNewEntry(selectedElement)}
                  className="px-2 py-1 bg-accent-themed text-white rounded text-xs hover:bg-accent-themed/80 transition-colors"
                  title="添加新条目"
                >
                  <Icons.Plus className="w-3 h-3" />
                </button>
              )}
            </div>
            <div className="flex-1 overflow-y-auto pr-2">
              {selectedElement && (() => {
                const element = worldBookElements.find(e => e.id === selectedElement);
                if (!element) return <div className="text-secondary-themed text-sm text-center py-8">未找到元素</div>;

                return (
                  <div className="space-y-3">
                    {renderElementMetadata(element)}
                    {element.subElements.map(subElement => (
                      <div key={subElement.id} className="p-3 bg-element-themed/20 rounded-lg border border-themed/30">
                        <div className="flex items-center justify-between mb-2">
                          <input
                            type="text"
                            value={subElement.key}
                            onChange={(e) => handleUpdateSubElement(element.id, subElement.id, { key: e.target.value })}
                            className="text-sm font-medium bg-element-themed border border-themed/30 rounded px-2 py-1 outline-none text-primary-themed flex-1 mr-2 focus:ring-1 focus:ring-accent-themed placeholder-themed"
                            placeholder="条目标题"
                          />
                          <div className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={subElement.isActive}
                              onChange={(e) => handleUpdateSubElement(element.id, subElement.id, { isActive: e.target.checked })}
                              className="w-4 h-4 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed"
                            />
                            <button
                              onClick={() => handleDeleteEntry(element.id, subElement.id)}
                              className="p-1 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded transition-colors"
                              title="删除条目"
                            >
                              <Icons.Trash className="w-3 h-3" />
                            </button>
                          </div>
                        </div>
                        <textarea
                          value={subElement.value}
                          onChange={(e) => handleUpdateSubElement(element.id, subElement.id, { value: e.target.value })}
                          className="w-full text-xs bg-element-themed border border-themed/30 rounded-md p-2 text-primary-themed resize-none focus:ring-1 focus:ring-accent-themed focus:border-accent-themed placeholder-themed"
                          rows={3}
                          placeholder="条目内容"
                        />
                        {renderSillyTavernMetadata(subElement, element.id)}
                      </div>
                    ))}
                    {element.subElements.length === 0 && (
                      <div className="text-center text-secondary-themed text-sm py-8">
                        该世界书暂无条目
                      </div>
                    )}
                  </div>
                );
              })()}
              {!selectedElement && (
                <div className="text-center text-secondary-themed text-sm py-8">
                  请选择一个世界书查看详情
                </div>
              )}
            </div>
          </div>

          {/* Right Panel - Testing */}
          <div className="w-1/3 flex flex-col min-w-0">
            <h4 className="text-sm font-semibold text-accent-themed mb-2 flex-shrink-0">触发测试</h4>

            {/* Scan Options */}
            <div className="mb-3 p-3 bg-element-themed/20 rounded-lg">
              <div className="grid grid-cols-2 gap-3 text-xs">
                <label className="flex items-center gap-2">
                  <span className="text-secondary-themed">扫描深度:</span>
                  <input
                    type="number"
                    value={scanOptions.scanDepth}
                    onChange={(e) => setScanOptions(prev => ({ ...prev, scanDepth: parseInt(e.target.value) || 4 }))}
                    className="w-12 px-2 py-1 bg-element-themed border border-themed/30 rounded text-primary-themed focus:ring-1 focus:ring-accent-themed"
                    min="1"
                    max="100"
                  />
                </label>
                <label className="flex items-center gap-2">
                  <span className="text-secondary-themed">令牌预算:</span>
                  <input
                    type="number"
                    value={scanOptions.tokenBudget}
                    onChange={(e) => setScanOptions(prev => ({ ...prev, tokenBudget: parseInt(e.target.value) || 2048 }))}
                    className="w-16 px-2 py-1 bg-element-themed border border-themed/30 rounded text-primary-themed focus:ring-1 focus:ring-accent-themed"
                    min="100"
                    max="10000"
                  />
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={scanOptions.recursiveScanning}
                    onChange={(e) => setScanOptions(prev => ({ ...prev, recursiveScanning: e.target.checked }))}
                    className="w-4 h-4 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed"
                  />
                  <span className="text-secondary-themed">递归扫描</span>
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={scanOptions.caseSensitive}
                    onChange={(e) => setScanOptions(prev => ({ ...prev, caseSensitive: e.target.checked }))}
                    className="w-4 h-4 text-accent-themed bg-element-themed border-themed rounded focus:ring-accent-themed"
                  />
                  <span className="text-secondary-themed">区分大小写</span>
                </label>
              </div>
            </div>

            {/* Test Input */}
            <textarea
              value={testText}
              onChange={(e) => setTestText(e.target.value)}
              placeholder="输入测试文本以检查哪些世界书条目会被触发..."
              className="w-full h-24 p-3 text-sm bg-element-themed border border-themed/30 rounded-md text-primary-themed resize-none mb-3 focus:ring-1 focus:ring-accent-themed focus:border-accent-themed placeholder-themed"
            />

            <button
              onClick={handleTestTriggers}
              className="mb-3 px-4 py-2 bg-accent-themed text-white rounded-md text-sm hover:bg-accent-themed/80 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={!testText.trim()}
            >
              测试触发
            </button>

            {/* Test Results */}
            {testResults && (
              <div className="flex-1 overflow-y-auto pr-2">
                <div className="text-sm space-y-3">
                  <div className="p-3 bg-element-themed/30 rounded-lg">
                    <div className="font-semibold text-accent-themed mb-2">测试结果:</div>
                    <div className="text-secondary-themed space-y-1">
                      <div>已激活: {testResults.activatedElements.length} 个条目</div>
                      <div>使用令牌: {testResults.totalTokensUsed}</div>
                      <div>递归激活: {testResults.recursiveActivations.length} 个条目</div>
                    </div>
                  </div>

                  {testResults.activatedElements.length > 0 && (
                    <div className="p-3 bg-green-500/10 rounded-lg border border-green-500/20">
                      <div className="font-semibold text-green-400 mb-2">已激活条目:</div>
                      <div className="space-y-1">
                        {testResults.activatedElements.map((entry: any, index: number) => (
                          <div key={index} className="text-sm text-secondary-themed">
                            • {entry.key}
                            {entry.sillyTavernData?.keys && (
                              <div className="text-xs text-secondary-themed/70 ml-4">
                                关键词: {entry.sillyTavernData.keys.join(', ')}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {testResults.activatedElements.length === 0 && (
                    <div className="p-3 bg-yellow-500/10 rounded-lg border border-yellow-500/20">
                      <div className="font-semibold text-yellow-400 mb-2">未找到匹配条目</div>
                      <div className="text-xs text-secondary-themed">
                        请检查：
                        <ul className="list-disc list-inside mt-1 space-y-1">
                          <li>关键词是否正确设置</li>
                          <li>条目是否已激活</li>
                          <li>世界书是否已激活</li>
                          <li>匹配选项（大小写、整词匹配）</li>
                        </ul>
                      </div>
                    </div>
                  )}

                  {testResults.activationLog.length > 0 && (
                    <div className="p-3 bg-element-themed/30 rounded-lg">
                      <div className="font-semibold text-accent-themed mb-2">激活日志:</div>
                      <div className="space-y-1">
                        {testResults.activationLog.map((log: string, index: number) => (
                          <div key={index} className="text-sm text-secondary-themed">
                            {log}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="mt-6 flex justify-end gap-3 flex-shrink-0 pt-4 border-t border-themed/20">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-element-themed text-primary-themed rounded-md hover:bg-element-themed/80 transition-colors"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};

export default WorldBookManager;
