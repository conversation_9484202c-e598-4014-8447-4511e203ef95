import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Icons } from '../constants';
import {
  switchAIProvider,
  validateAIProviders,
  updateAIProviderConfig,
  getCurrentAIProvider
} from '../services/aiService';
import { getAIServiceManager } from '../services/geminiClient';
import { AIProviderType } from '../services/aiProviders/types';
import { localConfigService } from '../services/localConfigService';

interface AIProviderModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ProviderInfo {
  available: boolean;
  current: boolean;
  models: any[];
  status: 'ok' | 'error';
  error?: string;
}

interface TestResult {
  success: boolean;
  responseTime: number;
  error?: string;
  modelCount?: number;
  testedModel?: string;
}

const providerNames: Record<AIProviderType, string> = {
  gemini: 'Gemini',
  openai: 'OpenAI GPT',
  anthropic: 'Claude',
  local: '本地AI',
  'azure-openai': 'Azure OpenAI',
  cohere: 'Cohere',
  huggingface: 'Hugging Face'
};

const providerIcons: Record<AIProviderType, string> = {
  gemini: '🤖',
  openai: '🧠',
  anthropic: '🎭',
  local: '🏠',
  'azure-openai': '☁️',
  cohere: '🔗',
  huggingface: '🤗'
};

const providerColors: Record<AIProviderType, string> = {
  gemini: 'bg-blue-500',
  openai: 'bg-green-500',
  anthropic: 'bg-purple-500',
  local: 'bg-gray-500',
  'azure-openai': 'bg-cyan-500',
  cohere: 'bg-orange-500',
  huggingface: 'bg-yellow-500'
};

export const AIProviderModal: React.FC<AIProviderModalProps> = ({ isOpen, onClose }) => {
  const [providers, setProviders] = useState<Record<AIProviderType, ProviderInfo>>({} as any);
  const [selectedProvider, setSelectedProvider] = useState<AIProviderType | null>(null);
  const [currentProvider, setCurrentProvider] = useState<AIProviderType | null>(null);
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<Record<AIProviderType, TestResult>>({} as any);
  const [testingProvider, setTestingProvider] = useState<AIProviderType | null>(null);
  const [loadingModels, setLoadingModels] = useState<Record<string, boolean>>({});
  const [availableModels, setAvailableModels] = useState<Record<string, any[]>>({});
  const [persistedModels, setPersistedModels] = useState<Record<string, any[]>>({});
  const [showAddProvider, setShowAddProvider] = useState(false);
  const [newProviderName, setNewProviderName] = useState('');
  const [newProviderUrl, setNewProviderUrl] = useState('');
  const [customProviders, setCustomProviders] = useState<Record<string, { name: string; url: string; apiKey: string }>>({});
  const [apiKeys, setApiKeys] = useState<Record<string, string>>({
    gemini: '',
    openai: '',
    anthropic: '',
    'azure-openai': '',
    local: '',
    cohere: '',
    huggingface: ''
  });
  const [customEndpoints, setCustomEndpoints] = useState<Record<string, string>>({
    openai: 'https://api.openai.com/v1',
    'azure-openai': 'https://your-resource.openai.azure.com',
    local: 'http://localhost:11434/v1',
    cohere: 'https://api.cohere.ai/v1',
    huggingface: 'https://api-inference.huggingface.co/models'
  });
  const [selectedModel, setSelectedModel] = useState<Record<string, string>>({});
  const [isUsingCache, setIsUsingCache] = useState(false);

  // Drag and drop states
  const [isDragging, setIsDragging] = useState(false);
  const [draggedProvider, setDraggedProvider] = useState<AIProviderType | null>(null);
  const [dragOverProvider, setDragOverProvider] = useState<AIProviderType | null>(null);
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null);
  const [providerOrder, setProviderOrder] = useState<AIProviderType[]>([]);
  const [isLongPressing, setIsLongPressing] = useState(false);

  useEffect(() => {
    if (isOpen) {
      // Reset drag states when modal opens
      setIsDragging(false);
      setDraggedProvider(null);
      setDragOverProvider(null);
      setIsLongPressing(false);
      if (longPressTimer) {
        clearTimeout(longPressTimer);
        setLongPressTimer(null);
      }
      document.body.style.userSelect = '';

      loadProviderData();
      loadPersistedModels();
      loadPersistedSelectedModels();
    }
  }, [isOpen]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (longPressTimer) {
        clearTimeout(longPressTimer);
      }
      document.body.style.userSelect = '';
    };
  }, [longPressTimer]);

  // Initialize provider order
  useEffect(() => {
    const savedOrder = localStorage.getItem('aiProviderOrder');
    if (savedOrder) {
      try {
        setProviderOrder(JSON.parse(savedOrder));
      } catch (error) {
        console.warn('Failed to parse saved provider order:', error);
      }
    }
  }, []);

  // Save provider order to localStorage
  const saveProviderOrder = (order: AIProviderType[]) => {
    localStorage.setItem('aiProviderOrder', JSON.stringify(order));
    setProviderOrder(order);
  };

  // Long press handlers
  const handleLongPressStart = (provider: AIProviderType, event: React.MouseEvent | React.TouchEvent) => {
    console.log('Long press start for provider:', provider);
    setIsLongPressing(true);

    const timer = setTimeout(() => {
      console.log('Long press activated for provider:', provider);
      setIsDragging(true);
      setDraggedProvider(provider);
      setIsLongPressing(false);

      // Add visual feedback
      document.body.style.userSelect = 'none';

      // Haptic feedback on mobile
      if ('vibrate' in navigator) {
        navigator.vibrate(50);
      }
    }, 300); // Reduced to 300ms for faster response

    setLongPressTimer(timer);
  };

  const handleLongPressEnd = () => {
    console.log('Long press end, isDragging:', isDragging, 'isLongPressing:', isLongPressing);
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }
    setIsLongPressing(false);
  };

  // Drag handlers
  const handleDragOver = (provider: AIProviderType, event: React.DragEvent) => {
    event.preventDefault();
    if (isDragging && draggedProvider && draggedProvider !== provider) {
      setDragOverProvider(provider);
    }
  };

  const handleDrop = (targetProvider: AIProviderType) => {
    console.log('Drop event - dragged:', draggedProvider, 'target:', targetProvider);

    if (!draggedProvider || draggedProvider === targetProvider) {
      console.log('Drop cancelled - same provider or no dragged provider');
      return;
    }

    // Get all current providers including custom ones
    const allProviderKeys = Object.keys(providers) as AIProviderType[];
    console.log('All provider keys:', allProviderKeys);

    // Use current order if available, otherwise use all providers
    let currentOrder = providerOrder.length > 0 ? [...providerOrder] : [...allProviderKeys];

    // Ensure all current providers are in the order array
    allProviderKeys.forEach(provider => {
      if (!currentOrder.includes(provider)) {
        currentOrder.push(provider);
      }
    });

    // Remove any providers that no longer exist
    currentOrder = currentOrder.filter(provider => allProviderKeys.includes(provider));

    console.log('Updated current order:', currentOrder);

    const draggedIndex = currentOrder.indexOf(draggedProvider);
    const targetIndex = currentOrder.indexOf(targetProvider);

    console.log('Dragged index:', draggedIndex, 'Target index:', targetIndex);

    if (draggedIndex !== -1 && targetIndex !== -1) {
      // Remove dragged item and insert at target position
      currentOrder.splice(draggedIndex, 1);
      currentOrder.splice(targetIndex, 0, draggedProvider);

      console.log('New order:', currentOrder);
      saveProviderOrder(currentOrder);
    } else {
      console.error('Failed to find provider indices:', {
        draggedProvider,
        targetProvider,
        draggedIndex,
        targetIndex,
        currentOrder
      });
    }

    // Reset drag state with a small delay to prevent accidental clicks
    setTimeout(() => {
      setIsDragging(false);
      setDraggedProvider(null);
      setDragOverProvider(null);
      document.body.style.userSelect = '';
    }, 100);
  };

  const handleDragEnd = (e?: React.DragEvent) => {
    console.log('Drag end event');
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    setIsDragging(false);
    setDraggedProvider(null);
    setDragOverProvider(null);
    document.body.style.userSelect = '';
  };

  // Get ordered providers list
  const getOrderedProviders = (): [AIProviderType, ProviderInfo][] => {
    const allProviders = Object.entries(providers) as [AIProviderType, ProviderInfo][];
    console.log('getOrderedProviders - all providers:', allProviders.map(([key]) => key));
    console.log('getOrderedProviders - provider order:', providerOrder);

    if (providerOrder.length === 0) {
      console.log('No saved order, returning all providers as-is');
      return allProviders;
    }

    // Sort providers according to saved order
    const orderedProviders: [AIProviderType, ProviderInfo][] = [];
    const remainingProviders = [...allProviders];

    // Add providers in saved order
    providerOrder.forEach(providerKey => {
      const index = remainingProviders.findIndex(([key]) => key === providerKey);
      if (index !== -1) {
        orderedProviders.push(remainingProviders.splice(index, 1)[0]);
        console.log(`Added ${providerKey} from saved order`);
      } else {
        console.log(`Provider ${providerKey} from saved order not found in current providers`);
      }
    });

    // Add any remaining providers that weren't in the saved order
    remainingProviders.forEach(([key]) => {
      console.log(`Adding remaining provider: ${key}`);
    });
    orderedProviders.push(...remainingProviders);

    console.log('Final ordered providers:', orderedProviders.map(([key]) => key));
    return orderedProviders;
  };

  const loadPersistedModels = () => {
    try {
      const saved = localStorage.getItem('ai-provider-models');
      if (saved) {
        const parsed = JSON.parse(saved);
        setPersistedModels(parsed);
      }
    } catch (error) {
      console.error('Failed to load persisted models:', error);
    }
  };

  const savePersistedModels = (models: Record<string, any[]>) => {
    try {
      localStorage.setItem('ai-provider-models', JSON.stringify(models));
      setPersistedModels(models);
    } catch (error) {
      console.error('Failed to save persisted models:', error);
    }
  };

  // 加载持久化的模型选择
  const loadPersistedSelectedModels = () => {
    try {
      const saved = localStorage.getItem('ai-provider-selected-models');
      if (saved) {
        const parsed = JSON.parse(saved);
        setSelectedModel(parsed);
      }
    } catch (error) {
      console.error('Failed to load persisted selected models:', error);
    }
  };

  // 保存模型选择
  const saveSelectedModel = (providerId: string, modelId: string) => {
    try {
      const updated = { ...selectedModel, [providerId]: modelId };
      localStorage.setItem('ai-provider-selected-models', JSON.stringify(updated));
      setSelectedModel(updated);
    } catch (error) {
      console.error('Failed to save selected model:', error);
    }
  };

  // 缓存供应商状态
  const cacheProviderData = (data: {
    providers: Record<string, ProviderInfo>;
    apiKeys: Record<string, string>;
    customEndpoints: Record<string, string>;
    customProviders: Record<string, { name: string; url: string; apiKey: string }>;
    currentProvider: AIProviderType | null;
    availableModels?: Record<string, any[]>;
    selectedModels?: Record<string, string>;
    timestamp: number;
  }) => {
    try {
      localStorage.setItem('ai-provider-cache', JSON.stringify(data));
    } catch (error) {
      console.error('Failed to cache provider data:', error);
    }
  };

  // 加载缓存的供应商状态
  const loadCachedProviderData = () => {
    try {
      const cached = localStorage.getItem('ai-provider-cache');
      if (cached) {
        const data = JSON.parse(cached);
        const now = Date.now();
        const cacheAge = now - data.timestamp;

        // 缓存有效期为10分钟，延长缓存时间减少加载频率
        if (cacheAge < 10 * 60 * 1000) {
          return data;
        }
      }
    } catch (error) {
      console.error('Failed to load cached provider data:', error);
    }
    return null;
  };

  // 清除缓存
  const clearProviderCache = () => {
    try {
      localStorage.removeItem('ai-provider-cache');
    } catch (error) {
      console.error('Failed to clear provider cache:', error);
    }
  };

  const loadProviderData = async (useCache = true) => {
    // 如果允许使用缓存，先尝试加载缓存数据
    if (useCache) {
      const cachedData = loadCachedProviderData();
      if (cachedData) {
        setProviders(cachedData.providers);
        setApiKeys(cachedData.apiKeys);
        setCustomEndpoints(cachedData.customEndpoints);
        setCustomProviders(cachedData.customProviders);
        setCurrentProvider(cachedData.currentProvider);
        setIsUsingCache(true);
        setLoading(false);

        // 同时加载缓存的模型数据
        if (cachedData.availableModels) {
          setAvailableModels(cachedData.availableModels);
        }
        if (cachedData.selectedModels) {
          setSelectedModel(cachedData.selectedModels);
        }

        // 在后台异步更新数据，延迟更长避免频繁刷新
        setTimeout(() => loadProviderData(false), 500);
        return;
      }
    }

    setLoading(true);
    try {
      const config = localConfigService.getConfig();

      // 只在初始化时设置API密钥，避免覆盖用户输入
      const newApiKeys = {
        gemini: config.ai?.geminiApiKey || apiKeys.gemini || '',
        openai: config.ai?.openaiApiKey || apiKeys.openai || '',
        anthropic: config.ai?.anthropicApiKey || apiKeys.anthropic || '',
        'azure-openai': config.ai?.openaiApiKey || apiKeys['azure-openai'] || '',
        local: apiKeys.local || '',
        cohere: apiKeys.cohere || '',
        huggingface: apiKeys.huggingface || ''
      };
      setApiKeys(newApiKeys);

      const newCustomEndpoints = {
        ...customEndpoints,
        openai: config.ai?.openaiBaseUrl || customEndpoints.openai || 'https://api.openai.com/v1',
        local: config.ai?.localEndpoint || customEndpoints.local || 'http://localhost:11434/v1'
      };
      setCustomEndpoints(newCustomEndpoints);

      const aiService = getAIServiceManager();
      const [providerStatus, validation, allModels] = await Promise.all([
        Promise.resolve(aiService.getProviderStatus()),
        validateAIProviders().catch(() => ({} as Record<AIProviderType, boolean>)),
        aiService.getAllAvailableModels().catch(() => ({} as Record<AIProviderType, any[]>))
      ]);

      const combinedProviders: Record<string, ProviderInfo> = {};
      const supportedProviders: AIProviderType[] = ['gemini', 'openai', 'anthropic', 'azure-openai', 'local'];

      for (const provider of supportedProviders) {
        const hasApiKey = newApiKeys[provider] || (provider === 'local');
        combinedProviders[provider] = {
          available: hasApiKey,
          current: provider === getCurrentAIProvider(),
          models: allModels[provider] || [],
          status: hasApiKey ? 'ok' : 'error',
          error: hasApiKey ? undefined : '需要配置API密钥'
        };
      }

      // 加载自定义供应商
      const customProvidersFromConfig = config.customProviders || {};
      setCustomProviders(customProvidersFromConfig);

      for (const [providerId, providerInfo] of Object.entries(customProvidersFromConfig)) {
        const hasApiKey = providerInfo.apiKey && providerInfo.apiKey.trim() !== '';
        combinedProviders[providerId] = {
          available: hasApiKey,
          current: providerId === getCurrentAIProvider(),
          models: [],
          status: hasApiKey ? 'ok' : 'error',
          error: hasApiKey ? undefined : '需要配置API密钥'
        };
      }

      const currentProvider = getCurrentAIProvider();
      setProviders(combinedProviders as Record<AIProviderType, ProviderInfo>);
      setCurrentProvider(currentProvider);

      // Load persisted models into available models
      setAvailableModels(prev => ({ ...prev, ...persistedModels }));

      if (!selectedProvider && supportedProviders.length > 0) {
        setSelectedProvider(supportedProviders[0]);
      }

      // 缓存数据，包括模型信息
      cacheProviderData({
        providers: combinedProviders,
        apiKeys: newApiKeys,
        customEndpoints: newCustomEndpoints,
        customProviders: customProvidersFromConfig,
        currentProvider,
        availableModels: { ...persistedModels, ...allModels },
        selectedModels: selectedModel,
        timestamp: Date.now()
      });

    } catch (error) {
      console.error('Failed to load provider data:', error);
    } finally {
      setLoading(false);
      setIsUsingCache(false);
    }
  };

  const handleProviderSwitch = async (provider: AIProviderType) => {
    setLoading(true);
    try {
      const success = await switchAIProvider(provider);
      if (success) {
        setCurrentProvider(provider);

        // Update local state immediately for better UX
        setProviders(prev => {
          const updated = { ...prev };
          // Set all providers to not current
          Object.keys(updated).forEach(key => {
            updated[key] = { ...updated[key], current: false };
          });
          // Set the selected provider as current
          if (updated[provider]) {
            updated[provider] = { ...updated[provider], current: true };
          }
          return updated;
        });

        // Reload data to ensure consistency
        await loadProviderData();
      }
    } catch (error) {
      console.error('Provider switch failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApiKeyUpdate = async (provider: AIProviderType, apiKey: string) => {
    try {
      const config = localConfigService.getConfig();
      const updatedConfig = { ...config };

      switch (provider) {
        case 'gemini':
          updatedConfig.ai = { ...updatedConfig.ai, geminiApiKey: apiKey };
          break;
        case 'openai':
          updatedConfig.ai = { ...updatedConfig.ai, openaiApiKey: apiKey };
          break;
        case 'anthropic':
          updatedConfig.ai = { ...updatedConfig.ai, anthropicApiKey: apiKey };
          break;
        case 'azure-openai':
          updatedConfig.ai = { ...updatedConfig.ai, openaiApiKey: apiKey };
          break;
      }

      localConfigService.updateConfig(updatedConfig);

      const providerConfig: any = { apiKey };
      if ((provider === 'openai' || provider === 'azure-openai') && customEndpoints[provider]) {
        providerConfig.baseUrl = customEndpoints[provider];
      }

      updateAIProviderConfig(provider, providerConfig);
      setApiKeys(prev => ({ ...prev, [provider]: apiKey }));

      // 清除缓存并重新加载
      clearProviderCache();
      setTimeout(() => loadProviderData(false), 500);
    } catch (error) {
      console.error('API key update failed:', error);
    }
  };

  const handleEndpointUpdate = async (provider: AIProviderType, endpoint: string) => {
    try {
      const config = localConfigService.getConfig();
      const updatedConfig = { ...config };

      if (provider === 'openai') {
        updatedConfig.ai = { ...updatedConfig.ai, openaiBaseUrl: endpoint };
      } else if (provider === 'local') {
        updatedConfig.ai = { ...updatedConfig.ai, localEndpoint: endpoint };
      }

      localConfigService.updateConfig(updatedConfig);
      setCustomEndpoints(prev => ({ ...prev, [provider]: endpoint }));

      updateAIProviderConfig(provider, {
        baseUrl: endpoint,
        apiKey: apiKeys[provider] || ''
      });

      // 清除缓存并重新加载
      clearProviderCache();
      setTimeout(() => loadProviderData(false), 500);
    } catch (error) {
      console.error('Endpoint update failed:', error);
    }
  };

  const handleFetchModels = async (provider: AIProviderType) => {
    setLoadingModels(prev => ({ ...prev, [provider]: true }));
    try {
      // 检查API密钥是否已配置
      const apiKey = customProviders[provider] ? customProviders[provider].apiKey : apiKeys[provider];
      if (!apiKey && provider !== 'local') {
        throw new Error('请先配置API密钥');
      }

      // 创建临时配置来获取模型列表
      const tempConfig: any = {};

      if (customProviders[provider]) {
        // 自定义供应商
        tempConfig.baseUrl = customProviders[provider].url;
        tempConfig.apiKey = customProviders[provider].apiKey;
      } else {
        // 内置供应商
        tempConfig.apiKey = apiKeys[provider];
        if (customEndpoints[provider]) {
          tempConfig.baseUrl = customEndpoints[provider];
        }
      }

      // 临时更新配置
      updateAIProviderConfig(provider, tempConfig);

      // 获取模型列表
      const aiService = getAIServiceManager();

      // 先切换到目标提供商
      const originalProvider = aiService.getCurrentProvider()?.getProviderType();
      const switched = aiService.switchProvider(provider);

      if (!switched) {
        throw new Error('无法切换到指定提供商');
      }

      // 获取当前提供商的模型列表
      const models = await aiService.getCurrentProviderModels();

      // 切换回原提供商
      if (originalProvider && originalProvider !== provider) {
        aiService.switchProvider(originalProvider);
      }

      setAvailableModels(prev => ({ ...prev, [provider]: models }));

      // Save to persistent storage
      const updatedModels = { ...persistedModels, [provider]: models };
      savePersistedModels(updatedModels);

      // 如果没有选择模型，自动选择第一个
      if (models.length > 0 && !selectedModel[provider]) {
        saveSelectedModel(provider, models[0].id);
      }

    } catch (error: any) {
      console.error(`Failed to fetch models for ${provider}:`, error);
      setAvailableModels(prev => ({ ...prev, [provider]: [] }));
      // 显示错误信息
      setTestResults(prev => ({
        ...prev,
        [provider]: {
          success: false,
          responseTime: 0,
          error: `获取模型失败: ${error.message}`
        }
      }));
    } finally {
      setLoadingModels(prev => ({ ...prev, [provider]: false }));
    }
  };

  const handleTestConnection = async (provider: AIProviderType, modelId?: string) => {
    setTestingProvider(provider);
    const startTime = Date.now();

    try {
      // 使用指定的模型进行测试
      const testModel = modelId || selectedModel[provider];

      if (!testModel) {
        throw new Error('请先选择一个模型进行测试');
      }

      // 检查API密钥
      const apiKey = customProviders[provider] ? customProviders[provider].apiKey : apiKeys[provider];
      if (!apiKey && provider !== 'local') {
        throw new Error('请先配置API密钥');
      }

      // 创建测试配置
      const testConfig: any = {
        defaultModel: testModel
      };

      if (customProviders[provider]) {
        testConfig.baseUrl = customProviders[provider].url;
        testConfig.apiKey = customProviders[provider].apiKey;
      } else {
        testConfig.apiKey = apiKeys[provider];
        if (customEndpoints[provider]) {
          testConfig.baseUrl = customEndpoints[provider];
        }
      }

      // 临时更新配置
      updateAIProviderConfig(provider, testConfig);

      // 获取AI服务管理器
      const aiService = getAIServiceManager();

      // 切换到目标提供商
      const originalProvider = aiService.getCurrentProvider()?.getProviderType();
      const switched = aiService.switchProvider(provider);

      if (!switched) {
        throw new Error('无法切换到指定提供商');
      }

      // 进行实际的API测试 - 发送一个简单的测试消息
      try {
        const testMessages = [
          { role: 'user' as const, content: 'Hello' }
        ];

        const response = await aiService.generateResponse(testMessages, {
          maxTokens: 10,
          temperature: 0.1
        });

        const responseTime = Date.now() - startTime;

        // 切换回原提供商
        if (originalProvider && originalProvider !== provider) {
          aiService.switchProvider(originalProvider);
        }

        setTestResults(prev => ({
          ...prev,
          [provider]: {
            success: true,
            responseTime,
            testedModel: testModel,
            modelCount: availableModels[provider]?.length || 0
          }
        }));

      } catch (apiError: any) {
        // 切换回原提供商
        if (originalProvider && originalProvider !== provider) {
          aiService.switchProvider(originalProvider);
        }

        const responseTime = Date.now() - startTime;
        throw new Error(`API调用失败: ${apiError.message}`);
      }

    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      setTestResults(prev => ({
        ...prev,
        [provider]: {
          success: false,
          responseTime,
          error: error.message || '测试失败',
          testedModel: modelId || selectedModel[provider]
        }
      }));
    } finally {
      setTestingProvider(null);
    }
  };

  const handleAddProvider = () => {
    setShowAddProvider(true);
    setNewProviderName('');
    setNewProviderUrl('');
  };

  const handleSaveNewProvider = () => {
    const trimmedName = newProviderName.trim();
    const trimmedUrl = newProviderUrl.trim();

    if (!trimmedName || !trimmedUrl) {
      alert('请填写完整的供应商名称和URL地址');
      return;
    }

    // 验证URL格式
    try {
      new URL(trimmedUrl);
    } catch {
      alert('请输入有效的URL地址');
      return;
    }

    const providerId = `custom_${Date.now()}`;
    const fullUrl = trimmedUrl.endsWith('/v1') ? trimmedUrl : `${trimmedUrl}/v1`;

    const newProvider = {
      name: trimmedName,
      url: fullUrl,
      apiKey: ''
    };

    setCustomProviders(prev => ({
      ...prev,
      [providerId]: newProvider
    }));

    // 保存到本地配置
    const config = localConfigService.getConfig();
    const updatedConfig = {
      ...config,
      customProviders: {
        ...config.customProviders,
        [providerId]: newProvider
      }
    };
    localConfigService.updateConfig(updatedConfig);

    setShowAddProvider(false);
    setNewProviderName('');
    setNewProviderUrl('');

    // 清除缓存并重新加载数据
    clearProviderCache();
    setTimeout(() => loadProviderData(false), 500);
  };

  const handleDeleteProvider = (providerId: string) => {
    if (!customProviders[providerId]) {
      alert('只能删除自定义供应商');
      return;
    }

    if (!confirm(`确定要删除供应商 "${customProviders[providerId].name}" 吗？`)) {
      return;
    }

    // 从状态中移除
    setCustomProviders(prev => {
      const newProviders = { ...prev };
      delete newProviders[providerId];
      return newProviders;
    });

    // 从本地配置中移除
    const config = localConfigService.getConfig();
    const updatedConfig = {
      ...config,
      customProviders: {
        ...config.customProviders
      }
    };
    delete updatedConfig.customProviders[providerId];
    localConfigService.updateConfig(updatedConfig);

    // 清理相关状态
    setAvailableModels(prev => {
      const newModels = { ...prev };
      delete newModels[providerId];
      return newModels;
    });

    // 清理选择的模型
    const updatedSelectedModels = { ...selectedModel };
    delete updatedSelectedModels[providerId];
    setSelectedModel(updatedSelectedModels);

    // 同时从localStorage中删除
    try {
      localStorage.setItem('ai-provider-selected-models', JSON.stringify(updatedSelectedModels));
    } catch (error) {
      console.error('Failed to save updated selected models:', error);
    }

    setTestResults(prev => {
      const newResults = { ...prev };
      delete newResults[providerId];
      return newResults;
    });

    // 如果删除的是当前选中的供应商，切换到第一个可用的
    if (selectedProvider === providerId) {
      const remainingProviders = Object.keys(providers).filter(p => p !== providerId);
      if (remainingProviders.length > 0) {
        setSelectedProvider(remainingProviders[0] as AIProviderType);
      } else {
        setSelectedProvider(null);
      }
    }

    // 清除缓存并重新加载数据
    clearProviderCache();
    setTimeout(() => loadProviderData(false), 500);
  };

  // 手动刷新数据
  const handleRefresh = () => {
    clearProviderCache();
    loadProviderData(false);
  };

  if (!isOpen) return null;

  const selectedProviderInfo = selectedProvider ? providers[selectedProvider] : null;
  const testResult = selectedProvider ? testResults[selectedProvider] : null;
  const isTesting = testingProvider === selectedProvider;

  // 获取供应商显示名称
  const getProviderName = (providerId: string) => {
    if (customProviders[providerId]) {
      return customProviders[providerId].name;
    }
    return providerNames[providerId as AIProviderType] || providerId;
  };

  // 获取供应商颜色
  const getProviderColor = (providerId: string) => {
    if (customProviders[providerId]) {
      return 'bg-purple-500'; // 自定义供应商使用紫色
    }
    return providerColors[providerId as AIProviderType] || 'bg-gray-500';
  };

  // 获取供应商图标
  const getProviderIcon = (providerId: string) => {
    if (customProviders[providerId]) {
      return '🔗'; // 自定义供应商使用链接图标
    }
    return providerIcons[providerId as AIProviderType] || '?';
  };

  // 格式化模型名称，将括号内容替换为AI供应商名称
  const formatModelName = (model: any, providerId: string) => {
    let modelName = model.name || model.id;

    // 检查是否有括号内容
    const bracketMatch = modelName.match(/\s*\(([^)]*)\)$/);
    if (bracketMatch) {
      // 去掉括号及其内容，然后添加供应商名称
      const baseName = modelName.replace(/\s*\([^)]*\)$/, '');
      const providerName = getProviderName(providerId);
      return `${baseName} (${providerName})`;
    }

    // 如果没有括号，对于自定义供应商添加供应商名称
    if (customProviders[providerId]) {
      return `${modelName} (${getProviderName(providerId)})`;
    }

    return modelName;
  };

  return createPortal(
    <div
      className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-[9999] p-4"
      onClick={(e) => {
        // Don't close modal if dragging or if the click is from a drag operation
        if (isDragging || isLongPressing) {
          console.log('Background clicked during drag, ignoring');
          return;
        }
        console.log('Background clicked, closing modal');
        onClose();
      }}
      role="dialog"
      aria-modal="true"
      aria-labelledby="ai-provider-modal-title"
    >
      <div
        className="bg-secondary-themed rounded-xl shadow-themed-xl border border-themed w-full max-w-4xl h-auto max-h-[85vh] flex overflow-hidden"
        onClick={(e) => {
          // Only stop propagation if not dragging
          if (!isDragging) {
            console.log('Modal content clicked, stopping propagation');
            e.stopPropagation();
          }
        }}
      >
        {/* Left Sidebar - Provider List */}
        <div className="w-80 bg-element-themed border-r border-themed flex flex-col">
          {/* Header */}
          <div className="p-6 border-b border-themed">
            <h2 id="ai-provider-modal-title" className="text-lg font-semibold text-accent-themed flex items-center">
              <Icons.Cog6Tooth className="w-5 h-5 mr-2" />
              AI 提供商设置
            </h2>
            <p className="text-sm text-secondary-themed mt-1">管理和配置AI服务提供商</p>
            <div className="flex items-center justify-between mt-1">
              <p className="text-xs text-secondary-themed flex items-center">
                <Icons.Bars3 className="w-3 h-3 mr-1" />
                长按拖拽可重新排序
              </p>
              {isUsingCache && (
                <div className="flex items-center text-xs text-accent-themed bg-accent-themed/10 px-2 py-1 rounded-full">
                  <Icons.Archive className="w-3 h-3 mr-1" />
                  使用缓存
                </div>
              )}
            </div>

          </div>
          
          {/* Provider List */}
          <div className="flex-1 overflow-y-auto p-4 space-y-2">
            {isDragging && (
              <div className="text-xs text-accent-themed bg-accent-themed/10 p-2 rounded-lg border border-accent-themed/20 mb-2">
                <div className="flex items-center">
                  <Icons.ArrowsUpDown className="w-3 h-3 mr-1" />
                  拖拽到目标位置来重新排序
                </div>
              </div>
            )}
            {getOrderedProviders().map(([provider, info]) => (
              <div
                key={provider}
                draggable={isDragging && draggedProvider === provider}
                onClick={() => !isDragging && setSelectedProvider(provider as AIProviderType)}
                onMouseDown={(e) => {
                  // 只在拖拽手柄上启用长按
                  const target = e.target as HTMLElement;
                  console.log('Mouse down on provider card, target:', target.className);
                  if (target.closest('.drag-handle')) {
                    console.log('Mouse down on drag handle for provider:', provider);
                    handleLongPressStart(provider as AIProviderType, e);
                  }
                }}
                onMouseUp={handleLongPressEnd}
                onMouseLeave={handleLongPressEnd}
                onTouchStart={(e) => {
                  const target = e.target as HTMLElement;
                  if (target.closest('.drag-handle')) {
                    handleLongPressStart(provider as AIProviderType, e);
                  }
                }}
                onTouchEnd={handleLongPressEnd}
                onDragStart={(e) => {
                  console.log('Drag start for provider:', provider);
                  e.dataTransfer.effectAllowed = 'move';
                  e.dataTransfer.setData('text/plain', provider);
                }}
                onDragOver={(e) => handleDragOver(provider as AIProviderType, e)}
                onDrop={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleDrop(provider as AIProviderType);
                }}
                onDragEnd={handleDragEnd}
                className={`p-4 rounded-lg border transition-all duration-200 select-none ${
                  isDragging && draggedProvider === provider
                    ? 'opacity-50 cursor-grabbing transform rotate-2 shadow-lg'
                    : isDragging && dragOverProvider === provider
                    ? 'border-accent-themed border-2 bg-accent-themed/20'
                    : isDragging
                    ? 'cursor-default opacity-75'
                    : isLongPressing
                    ? 'cursor-grab'
                    : 'cursor-pointer hover:shadow-md'
                } ${
                  selectedProvider === provider
                    ? 'bg-accent-themed/10 border-accent-themed shadow-md'
                    : 'bg-element-themed/50 border-themed/30 hover:bg-element-themed/80 hover:border-themed/50'
                }`}
              >
                <div className="flex items-center space-x-3">
                  {/* Drag Handle */}
                  <div className="drag-handle flex-shrink-0 opacity-40 hover:opacity-70 transition-opacity">
                    <Icons.Bars3 className="w-4 h-4 text-secondary-themed cursor-grab" />
                  </div>

                  {/* Radio button for current provider selection */}
                  <div className="flex-shrink-0">
                    <input
                      type="radio"
                      name="currentProvider"
                      checked={info.current}
                      onChange={(e) => {
                        e.stopPropagation();
                        if (!info.current && info.status === 'ok') {
                          handleProviderSwitch(provider as AIProviderType);
                        }
                      }}
                      disabled={info.status !== 'ok'}
                      className="w-4 h-4 text-accent-themed bg-white border-gray-300 focus:ring-accent-themed focus:ring-2 disabled:opacity-50"
                      title={info.status === 'ok' ? '点击设为默认提供商' : '请先配置并测试连接'}
                    />
                  </div>

                  <div className={`w-10 h-10 rounded-lg ${getProviderColor(provider)} flex items-center justify-center text-white text-sm font-medium shadow-sm`}>
                    {getProviderIcon(provider)}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="text-sm font-medium text-primary-themed truncate">
                        {getProviderName(provider)}
                      </h4>
                      {info.current && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          <div className="w-1.5 h-1.5 bg-green-500 rounded-full mr-1"></div>
                          当前使用
                        </span>
                      )}
                    </div>

                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${info.status === 'ok' ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <span className="text-xs text-secondary-themed">
                        {info.status === 'ok' ? '已连接' : info.error || '未连接'}
                      </span>
                      {info.models && info.models.length > 0 && (
                        <>
                          <span className="text-xs text-secondary-themed">•</span>
                          <span className="text-xs text-secondary-themed">
                            {info.models.length} 个模型
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {/* Add Provider Button */}
          <div className="p-4 border-t border-themed">
            <button
              onClick={handleAddProvider}
              className="w-full p-3 border border-dashed border-themed/50 rounded-lg text-secondary-themed hover:border-accent-themed hover:text-accent-themed hover:bg-accent-themed/5 transition-all duration-200 text-sm font-medium flex items-center justify-center space-x-2"
            >
              <Icons.Plus className="w-4 h-4" />
              <span>新增自定义供应商</span>
            </button>
          </div>
        </div>

        {/* Right Content - Provider Configuration */}
        <div className="flex-1 flex flex-col min-h-0">
          {/* Header */}
          <div className="p-6 border-b border-themed flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {selectedProvider && (
                <>
                  <div className={`w-8 h-8 rounded-lg ${getProviderColor(selectedProvider)} flex items-center justify-center text-white text-sm font-medium shadow-sm`}>
                    {getProviderIcon(selectedProvider)}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-primary-themed">
                      {getProviderName(selectedProvider)}
                    </h3>
                    <p className="text-sm text-secondary-themed">
                      {selectedProviderInfo?.current ? '当前使用的提供商' : '配置此提供商'}
                    </p>
                  </div>
                </>
              )}
            </div>
            <div className="flex items-center space-x-2">
              {/* Refresh button */}
              <button
                onClick={handleRefresh}
                disabled={loading}
                className="p-2 hover:bg-element-themed/50 rounded-lg transition-colors text-secondary-themed hover:text-primary-themed disabled:opacity-50"
                title="刷新数据并清除缓存"
              >
                {loading ? (
                  <div className="w-4 h-4 border border-current border-t-transparent rounded-full animate-spin"></div>
                ) : (
                  <Icons.ArrowPath className="w-4 h-4" />
                )}
              </button>

              {/* Delete button for custom providers */}
              {selectedProvider && customProviders[selectedProvider] && (
                <button
                  onClick={() => handleDeleteProvider(selectedProvider)}
                  className="p-2 hover:bg-red-500/10 rounded-lg transition-colors text-red-500 hover:text-red-600"
                  title="删除此自定义供应商"
                >
                  <Icons.Trash className="w-4 h-4" />
                </button>
              )}
              <button
                onClick={onClose}
                className="p-2 hover:bg-element-themed/50 rounded-lg transition-colors text-secondary-themed hover:text-primary-themed"
                aria-label="关闭"
              >
                <Icons.XMark className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          {selectedProvider && selectedProviderInfo ? (
            <div className="flex-1 p-6 overflow-y-auto space-y-6">
              {/* Connection Status */}
              <div className="bg-element-themed/30 rounded-xl p-4 border border-themed/20">
                <h4 className="text-sm font-semibold text-primary-themed mb-3 flex items-center">
                  <Icons.Signal className="w-4 h-4 mr-2" />
                  连接状态
                </h4>

                {testResult && (
                  <div className={`p-4 rounded-lg border ${
                    testResult.success
                      ? 'bg-green-50 border-green-200 text-green-800'
                      : 'bg-red-50 border-red-200 text-red-800'
                  }`}>
                    {testResult.success ? (
                      <div className="space-y-2">
                        <div className="flex items-center text-sm font-medium">
                          <Icons.CheckCircle className="w-4 h-4 mr-2" />
                          连接成功 - 响应时间: {testResult.responseTime}ms
                        </div>
                        {testResult.testedModel && (
                          <div className="text-xs">测试模型: {testResult.testedModel}</div>
                        )}
                        {testResult.modelCount && (
                          <div className="text-xs">可用模型: {testResult.modelCount} 个</div>
                        )}
                      </div>
                    ) : (
                      <div className="flex items-center text-sm font-medium">
                        <Icons.XCircle className="w-4 h-4 mr-2" />
                        连接失败: {testResult.error}
                      </div>
                    )}
                  </div>
                )}

                {!testResult && (
                  <div className="text-sm text-secondary-themed bg-element-themed/20 p-3 rounded-lg border border-dashed border-themed/30">
                    请先配置API密钥和地址，然后获取模型列表并测试连接
                  </div>
                )}
              </div>

              {/* API Configuration */}
              <div className="bg-element-themed/30 rounded-xl p-4 border border-themed/20 space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-semibold text-primary-themed flex items-center">
                    <Icons.Key className="w-4 h-4 mr-2" />
                    API 配置
                  </h4>
                  <button
                    onClick={() => handleFetchModels(selectedProvider)}
                    disabled={loadingModels[selectedProvider] || (!apiKeys[selectedProvider] && !customProviders[selectedProvider]?.apiKey && selectedProvider !== 'local')}
                    className="btn-dreamy text-xs px-3 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                    title={(!apiKeys[selectedProvider] && !customProviders[selectedProvider]?.apiKey && selectedProvider !== 'local') ? '请先配置API密钥' : '获取可用模型列表'}
                  >
                    {loadingModels[selectedProvider] ? (
                      <>
                        <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                        <span>获取中...</span>
                      </>
                    ) : (
                      <>
                        <Icons.ArrowPath className="w-3 h-3" />
                        <span>获取模型</span>
                      </>
                    )}
                  </button>
                </div>
                
                {/* API Key */}
                {selectedProvider !== 'local' && (
                  <div>
                    <label className="block text-sm font-medium text-primary-themed mb-2">API 密钥</label>
                    <div className="flex space-x-3">
                      <input
                        type="password"
                        value={customProviders[selectedProvider] ? customProviders[selectedProvider].apiKey : (apiKeys[selectedProvider] || '')}
                        onClick={(e) => {
                          console.log('Input clicked, stopping propagation');
                          e.stopPropagation();
                        }}
                        onFocus={(e) => {
                          console.log('Input focused, stopping propagation');
                          e.stopPropagation();
                        }}
                        onChange={(e) => {
                          const newValue = e.target.value;
                          if (customProviders[selectedProvider]) {
                            setCustomProviders(prev => ({
                              ...prev,
                              [selectedProvider]: {
                                ...prev[selectedProvider],
                                apiKey: newValue
                              }
                            }));

                            // 实时保存自定义供应商的API密钥
                            const config = localConfigService.getConfig();
                            const updatedConfig = {
                              ...config,
                              customProviders: {
                                ...config.customProviders,
                                [selectedProvider]: {
                                  ...config.customProviders?.[selectedProvider],
                                  ...customProviders[selectedProvider],
                                  apiKey: newValue
                                }
                              }
                            };
                            localConfigService.updateConfig(updatedConfig);
                          } else {
                            setApiKeys(prev => ({ ...prev, [selectedProvider]: newValue }));
                            // 实时保存内置供应商的API密钥
                            handleApiKeyUpdate(selectedProvider, newValue);
                          }
                        }}
                        placeholder="输入API密钥"
                        className="flex-1 p-3 bg-element-themed border border-themed rounded-lg text-primary-themed text-sm placeholder-themed focus:ring-2 focus:ring-accent-themed focus:border-accent-themed transition-colors"
                      />
                      <button
                        onClick={() => {
                          // 显示保存成功提示
                          setTestResults(prev => ({
                            ...prev,
                            [selectedProvider]: {
                              success: true,
                              responseTime: 0,
                              error: '配置已保存'
                            }
                          }));

                          // 清除提示
                          setTimeout(() => {
                            setTestResults(prev => {
                              const newResults = { ...prev };
                              delete newResults[selectedProvider];
                              return newResults;
                            });
                          }, 2000);
                        }}
                        className="px-4 py-3 bg-green-500 text-white rounded-lg text-sm hover:bg-green-600 transition-colors flex items-center space-x-1"
                        title="配置已自动保存，点击确认"
                      >
                        <Icons.Check className="w-4 h-4" />
                        <span>已保存</span>
                      </button>
                    </div>
                  </div>
                )}

                {/* Custom Provider URL Edit */}
                {customProviders[selectedProvider] && (
                  <div>
                    <label className="block text-sm font-medium text-primary-themed mb-2">API 地址</label>
                    <div className="flex space-x-3">
                      <input
                        type="url"
                        value={customProviders[selectedProvider].url}
                        onClick={(e) => e.stopPropagation()}
                        onFocus={(e) => e.stopPropagation()}
                        onChange={(e) => {
                          const newValue = e.target.value;
                          setCustomProviders(prev => ({
                            ...prev,
                            [selectedProvider]: {
                              ...prev[selectedProvider],
                              url: newValue
                            }
                          }));

                          // 实时保存自定义供应商的URL
                          const config = localConfigService.getConfig();
                          const updatedConfig = {
                            ...config,
                            customProviders: {
                              ...config.customProviders,
                              [selectedProvider]: {
                                ...config.customProviders?.[selectedProvider],
                                ...customProviders[selectedProvider],
                                url: newValue
                              }
                            }
                          };
                          localConfigService.updateConfig(updatedConfig);
                        }}
                        placeholder="例如：https://api.deepseek.com/v1"
                        className="flex-1 p-3 bg-element-themed border border-themed rounded-lg text-primary-themed text-sm placeholder-themed focus:ring-2 focus:ring-accent-themed focus:border-accent-themed transition-colors"
                      />
                      <button
                        onClick={() => {
                          // 显示保存成功提示
                          setTestResults(prev => ({
                            ...prev,
                            [selectedProvider]: {
                              success: true,
                              responseTime: 0,
                              error: 'URL已保存'
                            }
                          }));

                          // 清除提示
                          setTimeout(() => {
                            setTestResults(prev => {
                              const newResults = { ...prev };
                              delete newResults[selectedProvider];
                              return newResults;
                            });
                          }, 2000);
                        }}
                        className="px-4 py-3 bg-green-500 text-white rounded-lg text-sm hover:bg-green-600 transition-colors flex items-center space-x-1"
                        title="URL已自动保存，点击确认"
                      >
                        <Icons.Check className="w-4 h-4" />
                        <span>已保存</span>
                      </button>
                    </div>
                    <p className="text-xs text-secondary-themed mt-2 flex items-start">
                      <Icons.InformationCircle className="w-4 h-4 mr-1 mt-0.5 flex-shrink-0" />
                      修改后需要重新获取模型列表
                    </p>
                  </div>
                )}

                {/* Custom Endpoint */}
                {(selectedProvider === 'openai' || selectedProvider === 'local' || selectedProvider === 'azure-openai') && !customProviders[selectedProvider] && (
                  <div>
                    <label className="block text-sm text-secondary-themed mb-2">API 地址</label>
                    <div className="flex space-x-2">
                      <input
                        type="url"
                        value={customEndpoints[selectedProvider] || ''}
                        onClick={(e) => e.stopPropagation()}
                        onFocus={(e) => e.stopPropagation()}
                        onChange={(e) => {
                          const newValue = e.target.value;
                          setCustomEndpoints(prev => ({ ...prev, [selectedProvider]: newValue }));
                          // 实时保存自定义端点
                          handleEndpointUpdate(selectedProvider, newValue);
                        }}
                        placeholder="输入API地址"
                        className="flex-1 p-2 bg-element-themed border border-themed/30 rounded text-primary-themed text-sm"
                      />
                      <button
                        onClick={() => {
                          // 显示保存成功提示
                          setTestResults(prev => ({
                            ...prev,
                            [selectedProvider]: {
                              success: true,
                              responseTime: 0,
                              error: '端点已保存'
                            }
                          }));

                          // 清除提示
                          setTimeout(() => {
                            setTestResults(prev => {
                              const newResults = { ...prev };
                              delete newResults[selectedProvider];
                              return newResults;
                            });
                          }, 2000);
                        }}
                        className="px-4 py-2 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
                        title="端点已自动保存，点击确认"
                      >
                        已保存
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Model Selection */}
              <div className="bg-element-themed/30 rounded-xl p-4 border border-themed/20 space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-semibold text-primary-themed flex items-center">
                    <Icons.CpuChip className="w-4 h-4 mr-2" />
                    模型选择
                  </h4>
                  {availableModels[selectedProvider] && availableModels[selectedProvider].length > 0 && (
                    <button
                      onClick={() => handleTestConnection(selectedProvider)}
                      disabled={isTesting || !selectedModel[selectedProvider]}
                      className="btn-dreamy text-xs px-3 py-1.5 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-1"
                      title={!selectedModel[selectedProvider] ? '请先选择一个模型' : '使用选定模型测试API连接'}
                    >
                      {isTesting ? (
                        <>
                          <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin"></div>
                          <span>测试中...</span>
                        </>
                      ) : (
                        <>
                          <Icons.BeakerIcon className="w-3 h-3" />
                          <span>测试模型</span>
                        </>
                      )}
                    </button>
                  )}
                </div>

                {(availableModels[selectedProvider] && availableModels[selectedProvider].length > 0) ||
                 (persistedModels[selectedProvider] && persistedModels[selectedProvider].length > 0) ? (
                  <>
                    <select
                      value={selectedModel[selectedProvider] || ''}
                      onClick={(e) => e.stopPropagation()}
                      onFocus={(e) => e.stopPropagation()}
                      onChange={(e) => saveSelectedModel(selectedProvider, e.target.value)}
                      className="w-full p-3 bg-element-themed border border-themed rounded-lg text-primary-themed text-sm focus:ring-2 focus:ring-accent-themed focus:border-accent-themed transition-colors"
                    >
                      <option value="">请选择模型...</option>
                      {(availableModels[selectedProvider] || persistedModels[selectedProvider] || []).map((model) => (
                        <option key={model.id} value={model.id}>
                          {formatModelName(model, selectedProvider)} {model.maxTokens ? `(${model.maxTokens.toLocaleString()} tokens)` : ''}
                        </option>
                      ))}
                    </select>

                    {selectedModel[selectedProvider] && (
                      <div className="bg-accent-themed/10 border border-accent-themed/20 p-3 rounded-lg">
                        <div className="text-sm font-medium text-primary-themed mb-1">
                          已选择: {(() => {
                            const model = (availableModels[selectedProvider] || persistedModels[selectedProvider] || []).find(m => m.id === selectedModel[selectedProvider]);
                            return model ? formatModelName(model, selectedProvider) : selectedModel[selectedProvider];
                          })()}
                        </div>
                        {(availableModels[selectedProvider] || persistedModels[selectedProvider] || []).find(m => m.id === selectedModel[selectedProvider])?.maxTokens && (
                          <div className="text-xs text-secondary-themed">
                            最大tokens: {(availableModels[selectedProvider] || persistedModels[selectedProvider] || []).find(m => m.id === selectedModel[selectedProvider])?.maxTokens?.toLocaleString()}
                          </div>
                        )}
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-sm text-secondary-themed bg-element-themed/20 p-4 rounded-lg border border-dashed border-themed/30">
                    {loadingModels[selectedProvider] ? (
                      <div className="flex items-center space-x-3">
                        <div className="w-4 h-4 border-2 border-accent-themed border-t-transparent rounded-full animate-spin"></div>
                        <span>正在获取模型列表...</span>
                      </div>
                    ) : persistedModels[selectedProvider] && persistedModels[selectedProvider].length > 0 ? (
                      <div className="space-y-2">
                        <div className="flex items-center text-accent-themed">
                          <Icons.Archive className="w-4 h-4 mr-2" />
                          使用缓存的模型列表 ({persistedModels[selectedProvider].length} 个模型)
                        </div>
                        <div className="text-xs">点击"获取模型"按钮刷新最新列表</div>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <div className="flex items-center">
                          <Icons.ExclamationTriangle className="w-4 h-4 mr-2 text-yellow-500" />
                          暂无可用模型
                        </div>
                        <div className="text-xs">请先配置API密钥，然后点击"获取模型"按钮</div>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex justify-between items-center pt-6 border-t border-themed">
                <button
                  onClick={() => handleProviderSwitch(selectedProvider)}
                  disabled={loading || selectedProviderInfo.status === 'error' || selectedProviderInfo.current}
                  className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2 ${
                    selectedProviderInfo.current
                      ? 'bg-green-100 text-green-800 cursor-default border border-green-200'
                      : selectedProviderInfo.status === 'ok'
                      ? 'btn-dreamy'
                      : 'bg-gray-100 text-gray-400 cursor-not-allowed border border-gray-200'
                  }`}
                >
                  {selectedProviderInfo.current ? (
                    <>
                      <Icons.CheckCircle className="w-4 h-4" />
                      <span>当前使用</span>
                    </>
                  ) : (
                    <>
                      <Icons.ArrowRight className="w-4 h-4" />
                      <span>设为默认</span>
                    </>
                  )}
                </button>

                <button
                  onClick={onClose}
                  className="px-6 py-3 border border-themed rounded-lg text-sm text-secondary-themed hover:bg-element-themed/50 hover:text-primary-themed transition-all duration-200"
                >
                  关闭
                </button>
              </div>
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center text-secondary-themed">
              请选择一个AI提供商进行配置
            </div>
          )}
        </div>
      </div>

      {/* Add Provider Modal */}
      {showAddProvider && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-[10000]">
          <div
            className="bg-secondary-themed rounded-xl shadow-themed-xl border border-themed w-full max-w-md p-6 m-4"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-accent-themed flex items-center">
                <Icons.Plus className="w-5 h-5 mr-2" />
                新增 OpenAI 兼容供应商
              </h3>
              <button
                onClick={() => setShowAddProvider(false)}
                className="p-1 hover:bg-element-themed/50 rounded-lg transition-colors text-secondary-themed hover:text-primary-themed"
              >
                <Icons.XMark className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-primary-themed mb-2">供应商名称</label>
                <input
                  type="text"
                  value={newProviderName}
                  onClick={(e) => e.stopPropagation()}
                  onFocus={(e) => e.stopPropagation()}
                  onChange={(e) => setNewProviderName(e.target.value)}
                  placeholder="例如：DeepSeek、智谱AI、月之暗面等"
                  className="w-full p-3 bg-element-themed border border-themed rounded-lg text-primary-themed text-sm placeholder-themed focus:ring-2 focus:ring-accent-themed focus:border-accent-themed transition-colors"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-primary-themed mb-2">API 地址</label>
                <input
                  type="url"
                  value={newProviderUrl}
                  onClick={(e) => e.stopPropagation()}
                  onFocus={(e) => e.stopPropagation()}
                  onChange={(e) => setNewProviderUrl(e.target.value)}
                  placeholder="例如：https://api.deepseek.com"
                  className="w-full p-3 bg-element-themed border border-themed rounded-lg text-primary-themed text-sm placeholder-themed focus:ring-2 focus:ring-accent-themed focus:border-accent-themed transition-colors"
                />
                <p className="text-xs text-secondary-themed mt-2 flex items-start">
                  <Icons.InformationCircle className="w-4 h-4 mr-1 mt-0.5 flex-shrink-0" />
                  只需输入 /v1 前面的部分，系统会自动添加 /v1
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-themed">
              <button
                onClick={() => setShowAddProvider(false)}
                className="px-4 py-2 border border-themed rounded-lg text-sm text-secondary-themed hover:bg-element-themed/50 hover:text-primary-themed transition-all duration-200"
              >
                取消
              </button>
              <button
                onClick={handleSaveNewProvider}
                className="btn-dreamy px-4 py-2 text-sm flex items-center space-x-2"
              >
                <Icons.Plus className="w-4 h-4" />
                <span>添加供应商</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>,
    document.body
  );
};
