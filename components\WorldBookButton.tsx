import React, { useState } from 'react';
import { Icons } from '../constants';
import WorldBookManager from './WorldBookManager';
import { CustomNarrativePrimaryElement } from '../types';

interface WorldBookButtonProps {
  worldBookElements: CustomNarrativePrimaryElement[];
  onUpdateElements: (elements: CustomNarrativePrimaryElement[]) => void;
  className?: string;
  enableBackdropBlur?: boolean;
}

export const WorldBookButton: React.FC<WorldBookButtonProps> = ({ 
  worldBookElements,
  onUpdateElements,
  className = '',
  enableBackdropBlur = true
}) => {
  const [showModal, setShowModal] = useState(false);

  return (
    <>
      <button
        onClick={() => setShowModal(true)}
        className={`flex items-center space-x-2 px-4 py-2 bg-accent-themed text-white rounded-lg hover:bg-accent-themed/80 transition-colors ${className}`}
        title="世界书管理器"
      >
        <Icons.BookOpen className="w-5 h-5" />
        <span className="text-sm font-medium">世界书</span>
      </button>

      <WorldBookManager
        worldBookElements={worldBookElements}
        onUpdateElements={onUpdateElements}
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        enableBackdropBlur={enableBackdropBlur}
      />
    </>
  );
};
